import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../entities/cutting_master_entities.dart';

/// Repository interface for cutting master operations
abstract class CuttingMasterRepository {
  // Marker operations
  
  /// Create a new marker
  Future<Either<Failure, ApiResponse<Marker>>> createMarker({
    required String markerNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required double fabricWidth,
    required double markerLength,
    required int piecesPerMarker,
    required List<MarkerPiece> pieces,
    required String createdBy,
    required String createdByName,
    required double efficiency,
    String? notes,
    Map<String, dynamic> specifications = const {},
  });

  /// Get all markers
  Future<Either<Failure, ApiListResponse<Marker>>> getMarkers({
    MarkerStatus? status,
    String? productId,
    int? limit,
    String? lastDocumentId,
  });

  /// Get marker by ID
  Future<Either<Failure, ApiResponse<Marker>>> getMarkerById(String markerId);

  /// Update marker
  Future<Either<Failure, ApiResponse<Marker>>> updateMarker({
    required String markerId,
    String? markerNumber,
    String? productId,
    String? productName,
    String? fabricType,
    double? fabricWidth,
    double? markerLength,
    int? piecesPerMarker,
    List<MarkerPiece>? pieces,
    MarkerStatus? status,
    double? efficiency,
    String? notes,
    Map<String, dynamic>? specifications,
  });

  /// Approve marker
  Future<Either<Failure, ApiResponse<Marker>>> approveMarker({
    required String markerId,
    required String approvedBy,
    required String approvedByName,
  });

  /// Reject marker
  Future<Either<Failure, ApiResponse<Marker>>> rejectMarker({
    required String markerId,
    required String rejectedBy,
    required String rejectedByName,
    required String reason,
  });

  /// Delete marker
  Future<Either<Failure, ApiVoidResponse>> deleteMarker(String markerId);

  // Cutting task operations

  /// Create cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> createCuttingTask({
    required String taskNumber,
    required String markerId,
    required String markerNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required int plannedQuantity,
    required String assignedTo,
    required String assignedToName,
    required String assignedBy,
    required String assignedByName,
    required DateTime plannedStartDate,
    required DateTime plannedEndDate,
    String? notes,
    Map<String, dynamic> metadata = const {},
  });

  /// Get all cutting tasks
  Future<Either<Failure, ApiListResponse<CuttingTask>>> getCuttingTasks({
    CuttingTaskStatus? status,
    String? assignedTo,
    String? productId,
    int? limit,
    String? lastDocumentId,
  });

  /// Get cutting task by ID
  Future<Either<Failure, ApiResponse<CuttingTask>>> getCuttingTaskById(String taskId);

  /// Update cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> updateCuttingTask({
    required String taskId,
    String? taskNumber,
    int? actualQuantity,
    CuttingTaskStatus? status,
    DateTime? actualStartDate,
    DateTime? actualEndDate,
    List<CuttingPiece>? cutPieces,
    List<QualityCheck>? qualityChecks,
    String? notes,
    Map<String, dynamic>? metadata,
  });

  /// Start cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> startCuttingTask(String taskId);

  /// Complete cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> completeCuttingTask({
    required String taskId,
    required int actualQuantity,
    required List<CuttingPiece> cutPieces,
    List<QualityCheck>? qualityChecks,
    String? notes,
  });

  /// Delete cutting task
  Future<Either<Failure, ApiVoidResponse>> deleteCuttingTask(String taskId);

  // Fabric calculation operations

  /// Create fabric calculation
  Future<Either<Failure, ApiResponse<FabricCalculation>>> createFabricCalculation({
    required String calculationNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required double fabricWidth,
    required Map<String, int> sizeQuantities,
    required double totalFabricRequired,
    required double wastagePercentage,
    required double totalFabricWithWastage,
    required String calculatedBy,
    required String calculatedByName,
    String? notes,
  });

  /// Get all fabric calculations
  Future<Either<Failure, ApiListResponse<FabricCalculation>>> getFabricCalculations({
    FabricCalculationStatus? status,
    String? productId,
    int? limit,
    String? lastDocumentId,
  });

  /// Get fabric calculation by ID
  Future<Either<Failure, ApiResponse<FabricCalculation>>> getFabricCalculationById(String calculationId);

  /// Update fabric calculation
  Future<Either<Failure, ApiResponse<FabricCalculation>>> updateFabricCalculation({
    required String calculationId,
    String? calculationNumber,
    String? productId,
    String? productName,
    String? fabricType,
    double? fabricWidth,
    Map<String, int>? sizeQuantities,
    double? totalFabricRequired,
    double? wastagePercentage,
    double? totalFabricWithWastage,
    FabricCalculationStatus? status,
    String? notes,
  });

  /// Approve fabric calculation
  Future<Either<Failure, ApiResponse<FabricCalculation>>> approveFabricCalculation({
    required String calculationId,
    required String approvedBy,
    required String approvedByName,
  });

  /// Delete fabric calculation
  Future<Either<Failure, ApiVoidResponse>> deleteFabricCalculation(String calculationId);

  // Quality check operations

  /// Add quality check to cutting task
  Future<Either<Failure, ApiResponse<CuttingTask>>> addQualityCheck({
    required String taskId,
    required String checkType,
    required double score,
    required String checkedBy,
    required String checkedByName,
    String? notes,
    List<String> defects = const [],
  });

  /// Update quality check
  Future<Either<Failure, ApiResponse<CuttingTask>>> updateQualityCheck({
    required String taskId,
    required String checkId,
    String? checkType,
    double? score,
    QualityCheckStatus? status,
    String? notes,
    List<String>? defects,
  });

  // Production log operations

  /// Create production log entry
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> createProductionLogEntry({
    required String logNumber,
    required String taskId,
    required String taskType,
    required String operatorId,
    required String operatorName,
    required DateTime startTime,
    DateTime? endTime,
    required int quantityProduced,
    double? qualityScore,
    String? notes,
    Map<String, dynamic> metrics = const {},
  });

  /// Get production log entries
  Future<Either<Failure, ApiListResponse<ProductionLogEntry>>> getProductionLogEntries({
    String? taskId,
    String? operatorId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    String? lastDocumentId,
  });

  /// Get production log entry by ID
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> getProductionLogEntryById(String logId);

  /// Update production log entry
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> updateProductionLogEntry({
    required String logId,
    DateTime? endTime,
    int? quantityProduced,
    double? qualityScore,
    String? notes,
    Map<String, dynamic>? metrics,
  });

  /// Complete production log entry
  Future<Either<Failure, ApiResponse<ProductionLogEntry>>> completeProductionLogEntry({
    required String logId,
    required DateTime endTime,
    required int quantityProduced,
    double? qualityScore,
    String? notes,
  });

  /// Delete production log entry
  Future<Either<Failure, ApiVoidResponse>> deleteProductionLogEntry(String logId);

  // Analytics and reporting

  /// Get cutting task analytics
  Future<Either<Failure, Map<String, dynamic>>> getCuttingTaskAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String? productId,
  });

  /// Get marker efficiency report
  Future<Either<Failure, Map<String, dynamic>>> getMarkerEfficiencyReport({
    DateTime? startDate,
    DateTime? endDate,
    String? productId,
  });

  /// Get production summary
  Future<Either<Failure, Map<String, dynamic>>> getProductionSummary({
    DateTime? startDate,
    DateTime? endDate,
    String? operatorId,
  });
}
