import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../domain/entities/cutting_master_entities.dart';
import '../../domain/repositories/cutting_master_repository.dart';
import '../models/cutting_master_models.dart';

/// Firebase implementation of cutting master repository
class CuttingMasterRepositoryImpl implements CuttingMasterRepository {
  final FirebaseFirestore _firestore;

  // Collection names
  static const String _markersCollection = 'markers';
  static const String _cuttingTasksCollection = 'cutting_tasks';
  static const String _fabricCalculationsCollection = 'fabric_calculations';
  static const String _productionLogsCollection = 'production_logs';

  CuttingMasterRepositoryImpl(this._firestore);

  @override
  Future<Either<Failure, ApiResponse<Marker>>> createMarker({
    required String markerNumber,
    required String productId,
    required String productName,
    required String fabricType,
    required double fabricWidth,
    required double markerLength,
    required int piecesPerMarker,
    required List<MarkerPiece> pieces,
    required String createdBy,
    required String createdByName,
    required double efficiency,
    String? notes,
    Map<String, dynamic> specifications = const {},
  }) async {
    try {
      final now = Timestamp.now();
      final docRef = _firestore.collection(_markersCollection).doc();

      final marker = Marker(
        id: docRef.id,
        markerNumber: markerNumber,
        productId: productId,
        productName: productName,
        fabricType: fabricType,
        fabricWidth: fabricWidth,
        markerLength: markerLength,
        piecesPerMarker: piecesPerMarker,
        pieces: pieces,
        status: MarkerStatus.draft,
        createdBy: createdBy,
        createdByName: createdByName,
        efficiency: efficiency,
        notes: notes,
        specifications: specifications,
        createdAt: now.toDate(),
        updatedAt: now.toDate(),
      );

      final markerModel = MarkerModel.fromEntity(marker);
      await docRef.set(markerModel.toFirestore());

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker created successfully',
        data: marker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to create marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiListResponse<Marker>>> getMarkers({
    MarkerStatus? status,
    String? productId,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = _firestore.collection(_markersCollection);

      // Apply filters
      if (status != null) {
        query = query.where('status', isEqualTo: status.name);
      }
      if (productId != null) {
        query = query.where('productId', isEqualTo: productId);
      }

      // Apply pagination
      if (lastDocumentId != null) {
        final lastDoc = await _firestore
            .collection(_markersCollection)
            .doc(lastDocumentId)
            .get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      // Order by creation date
      query = query.orderBy('createdAt', descending: true);

      final querySnapshot = await query.get();
      final markers = querySnapshot.docs
          .map((doc) => MarkerModel.fromFirestore(doc).toEntity())
          .toList();

      return Right(ApiListResponse<Marker>(
        success: true,
        message: 'Markers retrieved successfully',
        data: markers,
        pagination: PaginationMeta(
          currentPage: 1,
          totalPages: 1,
          totalItems: markers.length,
          hasNextPage: querySnapshot.docs.length == (limit ?? 20),
        ),
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get markers: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> getMarkerById(String markerId) async {
    try {
      final doc = await _firestore.collection(_markersCollection).doc(markerId).get();

      if (!doc.exists) {
        return Left(NotFoundFailure('Marker not found'));
      }

      final marker = MarkerModel.fromFirestore(doc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker retrieved successfully',
        data: marker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> updateMarker({
    required String markerId,
    String? markerNumber,
    String? productId,
    String? productName,
    String? fabricType,
    double? fabricWidth,
    double? markerLength,
    int? piecesPerMarker,
    List<MarkerPiece>? pieces,
    MarkerStatus? status,
    double? efficiency,
    String? notes,
    Map<String, dynamic>? specifications,
  }) async {
    try {
      final docRef = _firestore.collection(_markersCollection).doc(markerId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return Left(NotFoundFailure('Marker not found'));
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.now(),
      };

      if (markerNumber != null) updateData['markerNumber'] = markerNumber;
      if (productId != null) updateData['productId'] = productId;
      if (productName != null) updateData['productName'] = productName;
      if (fabricType != null) updateData['fabricType'] = fabricType;
      if (fabricWidth != null) updateData['fabricWidth'] = fabricWidth;
      if (markerLength != null) updateData['markerLength'] = markerLength;
      if (piecesPerMarker != null) updateData['piecesPerMarker'] = piecesPerMarker;
      if (pieces != null) {
        updateData['pieces'] = pieces
            .map((piece) => MarkerPieceModel.fromEntity(piece).toMap())
            .toList();
      }
      if (status != null) updateData['status'] = status.name;
      if (efficiency != null) updateData['efficiency'] = efficiency;
      if (notes != null) updateData['notes'] = notes;
      if (specifications != null) updateData['specifications'] = specifications;

      await docRef.update(updateData);

      // Get updated marker
      final updatedDoc = await docRef.get();
      final updatedMarker = MarkerModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker updated successfully',
        data: updatedMarker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> approveMarker({
    required String markerId,
    required String approvedBy,
    required String approvedByName,
  }) async {
    try {
      final docRef = _firestore.collection(_markersCollection).doc(markerId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return Left(NotFoundFailure('Marker not found'));
      }

      await docRef.update({
        'status': MarkerStatus.approved.name,
        'approvedBy': approvedBy,
        'approvedByName': approvedByName,
        'approvedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      // Get updated marker
      final updatedDoc = await docRef.get();
      final updatedMarker = MarkerModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker approved successfully',
        data: updatedMarker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to approve marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiResponse<Marker>>> rejectMarker({
    required String markerId,
    required String rejectedBy,
    required String rejectedByName,
    required String reason,
  }) async {
    try {
      final docRef = _firestore.collection(_markersCollection).doc(markerId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return Left(NotFoundFailure('Marker not found'));
      }

      await docRef.update({
        'status': MarkerStatus.rejected.name,
        'rejectedBy': rejectedBy,
        'rejectedByName': rejectedByName,
        'rejectionReason': reason,
        'rejectedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      // Get updated marker
      final updatedDoc = await docRef.get();
      final updatedMarker = MarkerModel.fromFirestore(updatedDoc).toEntity();

      return Right(ApiResponse<Marker>(
        success: true,
        message: 'Marker rejected successfully',
        data: updatedMarker,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to reject marker: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ApiVoidResponse>> deleteMarker(String markerId) async {
    try {
      await _firestore.collection(_markersCollection).doc(markerId).update({
        'deletedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return Right(ApiVoidResponse(
        success: true,
        message: 'Marker deleted successfully',
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to delete marker: ${e.toString()}'));
    }
  }
