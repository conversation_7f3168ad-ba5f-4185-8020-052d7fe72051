import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/error/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/manufacturing_task_entities.dart';
import '../../domain/entities/manufacturing_task_analytics.dart';
import '../../domain/models/manufacturing_task_requests.dart';
import '../../domain/usecases/manufacturing_task_usecases.dart';

part 'manufacturing_task_event.dart';
part 'manufacturing_task_state.dart';

/// BLoC for managing manufacturing task operations
class ManufacturingTaskBloc extends Bloc<ManufacturingTaskEvent, ManufacturingTaskState> {
  final GetManufacturingTaskOrdersUseCase _getManufacturingTaskOrdersUseCase;
  final GetManufacturingTaskOrderByIdUseCase _getManufacturingTaskOrderByIdUseCase;
  final CreateManufacturingTaskOrderUseCase _createManufacturingTaskOrderUseCase;
  final UpdateManufacturingTaskOrderUseCase _updateManufacturingTaskOrderUseCase;
  final DeleteManufacturingTaskOrderUseCase _deleteManufacturingTaskOrderUseCase;
  final StartManufacturingTaskOrderUseCase _startManufacturingTaskOrderUseCase;
  final PauseManufacturingTaskOrderUseCase _pauseManufacturingTaskOrderUseCase;
  final ResumeManufacturingTaskOrderUseCase _resumeManufacturingTaskOrderUseCase;
  final CompleteManufacturingTaskOrderUseCase _completeManufacturingTaskOrderUseCase;
  
  final GetStageTasksForOrderUseCase _getStageTasksForOrderUseCase;
  final GetStageTasksByDepartmentUseCase _getStageTasksByDepartmentUseCase;
  final GetStageTasksByStageUseCase _getStageTasksByStageUseCase;
  final GetStageTaskByIdUseCase _getStageTaskByIdUseCase;
  final CreateStageTaskUseCase _createStageTaskUseCase;
  final UpdateStageTaskUseCase _updateStageTaskUseCase;
  final StartStageTaskUseCase _startStageTaskUseCase;
  final CompleteStageTaskUseCase _completeStageTaskUseCase;
  final AssignWorkersToStageTaskUseCase _assignWorkersToStageTaskUseCase;
  final UpdateStageTaskProgressUseCase _updateStageTaskProgressUseCase;
  
  final CreateStageTransferUseCase _createStageTransferUseCase;
  final GetTransfersForOrderUseCase _getTransfersForOrderUseCase;
  final GetPendingTransfersForDepartmentUseCase _getPendingTransfersForDepartmentUseCase;
  final AcceptStageTransferUseCase _acceptStageTransferUseCase;
  final RejectStageTransferUseCase _rejectStageTransferUseCase;
  final CompleteStageTransferUseCase _completeStageTransferUseCase;
  
  final AddNoteToOrderUseCase _addNoteToOrderUseCase;
  final AddNoteToStageTaskUseCase _addNoteToStageTaskUseCase;
  final GetNotesForOrderUseCase _getNotesForOrderUseCase;
  final GetNotesForStageTaskUseCase _getNotesForStageTaskUseCase;
  final UpdateNoteUseCase _updateNoteUseCase;
  final DeleteNoteUseCase _deleteNoteUseCase;
  
  final GetManufacturingTaskAnalyticsUseCase _getManufacturingTaskAnalyticsUseCase;
  final GetStagePerformanceMetricsUseCase _getStagePerformanceMetricsUseCase;
  final GetBottleneckAnalysisUseCase _getBottleneckAnalysisUseCase;
  final GetEfficiencyMetricsUseCase _getEfficiencyMetricsUseCase;
  
  final SearchManufacturingTaskOrdersUseCase _searchManufacturingTaskOrdersUseCase;
  final GetOverdueManufacturingTaskOrdersUseCase _getOverdueManufacturingTaskOrdersUseCase;
  final GetUrgentManufacturingTaskOrdersUseCase _getUrgentManufacturingTaskOrdersUseCase;
  final GetManufacturingTaskOrdersByStatusUseCase _getManufacturingTaskOrdersByStatusUseCase;
  final GetStageTasksByWorkerUseCase _getStageTasksByWorkerUseCase;
  final GetStageTasksByStatusUseCase _getStageTasksByStatusUseCase;

  ManufacturingTaskBloc({
    required GetManufacturingTaskOrdersUseCase getManufacturingTaskOrdersUseCase,
    required GetManufacturingTaskOrderByIdUseCase getManufacturingTaskOrderByIdUseCase,
    required CreateManufacturingTaskOrderUseCase createManufacturingTaskOrderUseCase,
    required UpdateManufacturingTaskOrderUseCase updateManufacturingTaskOrderUseCase,
    required DeleteManufacturingTaskOrderUseCase deleteManufacturingTaskOrderUseCase,
    required StartManufacturingTaskOrderUseCase startManufacturingTaskOrderUseCase,
    required PauseManufacturingTaskOrderUseCase pauseManufacturingTaskOrderUseCase,
    required ResumeManufacturingTaskOrderUseCase resumeManufacturingTaskOrderUseCase,
    required CompleteManufacturingTaskOrderUseCase completeManufacturingTaskOrderUseCase,
    required GetStageTasksForOrderUseCase getStageTasksForOrderUseCase,
    required GetStageTasksByDepartmentUseCase getStageTasksByDepartmentUseCase,
    required GetStageTasksByStageUseCase getStageTasksByStageUseCase,
    required GetStageTaskByIdUseCase getStageTaskByIdUseCase,
    required CreateStageTaskUseCase createStageTaskUseCase,
    required UpdateStageTaskUseCase updateStageTaskUseCase,
    required StartStageTaskUseCase startStageTaskUseCase,
    required CompleteStageTaskUseCase completeStageTaskUseCase,
    required AssignWorkersToStageTaskUseCase assignWorkersToStageTaskUseCase,
    required UpdateStageTaskProgressUseCase updateStageTaskProgressUseCase,
    required CreateStageTransferUseCase createStageTransferUseCase,
    required GetTransfersForOrderUseCase getTransfersForOrderUseCase,
    required GetPendingTransfersForDepartmentUseCase getPendingTransfersForDepartmentUseCase,
    required AcceptStageTransferUseCase acceptStageTransferUseCase,
    required RejectStageTransferUseCase rejectStageTransferUseCase,
    required CompleteStageTransferUseCase completeStageTransferUseCase,
    required AddNoteToOrderUseCase addNoteToOrderUseCase,
    required AddNoteToStageTaskUseCase addNoteToStageTaskUseCase,
    required GetNotesForOrderUseCase getNotesForOrderUseCase,
    required GetNotesForStageTaskUseCase getNotesForStageTaskUseCase,
    required UpdateNoteUseCase updateNoteUseCase,
    required DeleteNoteUseCase deleteNoteUseCase,
    required GetManufacturingTaskAnalyticsUseCase getManufacturingTaskAnalyticsUseCase,
    required GetStagePerformanceMetricsUseCase getStagePerformanceMetricsUseCase,
    required GetBottleneckAnalysisUseCase getBottleneckAnalysisUseCase,
    required GetEfficiencyMetricsUseCase getEfficiencyMetricsUseCase,
    required SearchManufacturingTaskOrdersUseCase searchManufacturingTaskOrdersUseCase,
    required GetOverdueManufacturingTaskOrdersUseCase getOverdueManufacturingTaskOrdersUseCase,
    required GetUrgentManufacturingTaskOrdersUseCase getUrgentManufacturingTaskOrdersUseCase,
    required GetManufacturingTaskOrdersByStatusUseCase getManufacturingTaskOrdersByStatusUseCase,
    required GetStageTasksByWorkerUseCase getStageTasksByWorkerUseCase,
    required GetStageTasksByStatusUseCase getStageTasksByStatusUseCase,
  })  : _getManufacturingTaskOrdersUseCase = getManufacturingTaskOrdersUseCase,
        _getManufacturingTaskOrderByIdUseCase = getManufacturingTaskOrderByIdUseCase,
        _createManufacturingTaskOrderUseCase = createManufacturingTaskOrderUseCase,
        _updateManufacturingTaskOrderUseCase = updateManufacturingTaskOrderUseCase,
        _deleteManufacturingTaskOrderUseCase = deleteManufacturingTaskOrderUseCase,
        _startManufacturingTaskOrderUseCase = startManufacturingTaskOrderUseCase,
        _pauseManufacturingTaskOrderUseCase = pauseManufacturingTaskOrderUseCase,
        _resumeManufacturingTaskOrderUseCase = resumeManufacturingTaskOrderUseCase,
        _completeManufacturingTaskOrderUseCase = completeManufacturingTaskOrderUseCase,
        _getStageTasksForOrderUseCase = getStageTasksForOrderUseCase,
        _getStageTasksByDepartmentUseCase = getStageTasksByDepartmentUseCase,
        _getStageTasksByStageUseCase = getStageTasksByStageUseCase,
        _getStageTaskByIdUseCase = getStageTaskByIdUseCase,
        _createStageTaskUseCase = createStageTaskUseCase,
        _updateStageTaskUseCase = updateStageTaskUseCase,
        _startStageTaskUseCase = startStageTaskUseCase,
        _completeStageTaskUseCase = completeStageTaskUseCase,
        _assignWorkersToStageTaskUseCase = assignWorkersToStageTaskUseCase,
        _updateStageTaskProgressUseCase = updateStageTaskProgressUseCase,
        _createStageTransferUseCase = createStageTransferUseCase,
        _getTransfersForOrderUseCase = getTransfersForOrderUseCase,
        _getPendingTransfersForDepartmentUseCase = getPendingTransfersForDepartmentUseCase,
        _acceptStageTransferUseCase = acceptStageTransferUseCase,
        _rejectStageTransferUseCase = rejectStageTransferUseCase,
        _completeStageTransferUseCase = completeStageTransferUseCase,
        _addNoteToOrderUseCase = addNoteToOrderUseCase,
        _addNoteToStageTaskUseCase = addNoteToStageTaskUseCase,
        _getNotesForOrderUseCase = getNotesForOrderUseCase,
        _getNotesForStageTaskUseCase = getNotesForStageTaskUseCase,
        _updateNoteUseCase = updateNoteUseCase,
        _deleteNoteUseCase = deleteNoteUseCase,
        _getManufacturingTaskAnalyticsUseCase = getManufacturingTaskAnalyticsUseCase,
        _getStagePerformanceMetricsUseCase = getStagePerformanceMetricsUseCase,
        _getBottleneckAnalysisUseCase = getBottleneckAnalysisUseCase,
        _getEfficiencyMetricsUseCase = getEfficiencyMetricsUseCase,
        _searchManufacturingTaskOrdersUseCase = searchManufacturingTaskOrdersUseCase,
        _getOverdueManufacturingTaskOrdersUseCase = getOverdueManufacturingTaskOrdersUseCase,
        _getUrgentManufacturingTaskOrdersUseCase = getUrgentManufacturingTaskOrdersUseCase,
        _getManufacturingTaskOrdersByStatusUseCase = getManufacturingTaskOrdersByStatusUseCase,
        _getStageTasksByWorkerUseCase = getStageTasksByWorkerUseCase,
        _getStageTasksByStatusUseCase = getStageTasksByStatusUseCase,
        super(const ManufacturingTaskInitial()) {
    
    // Manufacturing Task Order Events
    on<LoadManufacturingTaskOrdersRequested>(_onLoadManufacturingTaskOrdersRequested);
    on<LoadManufacturingTaskOrderByIdRequested>(_onLoadManufacturingTaskOrderByIdRequested);
    on<CreateManufacturingTaskOrderRequested>(_onCreateManufacturingTaskOrderRequested);
    on<UpdateManufacturingTaskOrderRequested>(_onUpdateManufacturingTaskOrderRequested);
    on<DeleteManufacturingTaskOrderRequested>(_onDeleteManufacturingTaskOrderRequested);
    on<StartManufacturingTaskOrderRequested>(_onStartManufacturingTaskOrderRequested);
    on<PauseManufacturingTaskOrderRequested>(_onPauseManufacturingTaskOrderRequested);
    on<ResumeManufacturingTaskOrderRequested>(_onResumeManufacturingTaskOrderRequested);
    on<CompleteManufacturingTaskOrderRequested>(_onCompleteManufacturingTaskOrderRequested);
    
    // Stage Task Events
    on<LoadStageTasksForOrderRequested>(_onLoadStageTasksForOrderRequested);
    on<LoadStageTasksByDepartmentRequested>(_onLoadStageTasksByDepartmentRequested);
    on<LoadStageTasksByStageRequested>(_onLoadStageTasksByStageRequested);
    on<LoadStageTaskByIdRequested>(_onLoadStageTaskByIdRequested);
    on<CreateStageTaskRequested>(_onCreateStageTaskRequested);
    on<UpdateStageTaskRequested>(_onUpdateStageTaskRequested);
    on<StartStageTaskRequested>(_onStartStageTaskRequested);
    on<CompleteStageTaskRequested>(_onCompleteStageTaskRequested);
    on<AssignWorkersToStageTaskRequested>(_onAssignWorkersToStageTaskRequested);
    on<UpdateStageTaskProgressRequested>(_onUpdateStageTaskProgressRequested);
    
    // Stage Transfer Events
    on<CreateStageTransferRequested>(_onCreateStageTransferRequested);
    on<LoadTransfersForOrderRequested>(_onLoadTransfersForOrderRequested);
    on<LoadPendingTransfersForDepartmentRequested>(_onLoadPendingTransfersForDepartmentRequested);
    on<AcceptStageTransferRequested>(_onAcceptStageTransferRequested);
    on<RejectStageTransferRequested>(_onRejectStageTransferRequested);
    on<CompleteStageTransferRequested>(_onCompleteStageTransferRequested);
    
    // Notes Events
    on<AddNoteToOrderRequested>(_onAddNoteToOrderRequested);
    on<AddNoteToStageTaskRequested>(_onAddNoteToStageTaskRequested);
    on<LoadNotesForOrderRequested>(_onLoadNotesForOrderRequested);
    on<LoadNotesForStageTaskRequested>(_onLoadNotesForStageTaskRequested);
    on<UpdateNoteRequested>(_onUpdateNoteRequested);
    on<DeleteNoteRequested>(_onDeleteNoteRequested);
    
    // Analytics Events
    on<LoadManufacturingTaskAnalyticsRequested>(_onLoadManufacturingTaskAnalyticsRequested);
    on<LoadStagePerformanceMetricsRequested>(_onLoadStagePerformanceMetricsRequested);
    on<LoadBottleneckAnalysisRequested>(_onLoadBottleneckAnalysisRequested);
    on<LoadEfficiencyMetricsRequested>(_onLoadEfficiencyMetricsRequested);
    
    // Search and Filter Events
    on<SearchManufacturingTaskOrdersRequested>(_onSearchManufacturingTaskOrdersRequested);
    on<LoadOverdueManufacturingTaskOrdersRequested>(_onLoadOverdueManufacturingTaskOrdersRequested);
    on<LoadUrgentManufacturingTaskOrdersRequested>(_onLoadUrgentManufacturingTaskOrdersRequested);
    on<LoadManufacturingTaskOrdersByStatusRequested>(_onLoadManufacturingTaskOrdersByStatusRequested);
    on<LoadStageTasksByWorkerRequested>(_onLoadStageTasksByWorkerRequested);
    on<LoadStageTasksByStatusRequested>(_onLoadStageTasksByStatusRequested);
    
    // Utility Events
    on<RefreshManufacturingTaskDataRequested>(_onRefreshManufacturingTaskDataRequested);
    on<ClearManufacturingTaskStateRequested>(_onClearManufacturingTaskStateRequested);
  }

  // Manufacturing Task Order Event Handlers

  /// Load manufacturing task orders
  Future<void> _onLoadManufacturingTaskOrdersRequested(
    LoadManufacturingTaskOrdersRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getManufacturingTaskOrdersUseCase(
      GetManufacturingTaskOrdersParams(
        filter: event.filter,
        pagination: event.pagination,
      ),
    );

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrdersLoaded(
            orders: response.data!,
            pagination: response.pagination,
          ));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load manufacturing task orders'));
        }
      },
    );
  }

  /// Load manufacturing task order by ID
  Future<void> _onLoadManufacturingTaskOrderByIdRequested(
    LoadManufacturingTaskOrderByIdRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _getManufacturingTaskOrderByIdUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderLoaded(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to load manufacturing task order'));
        }
      },
    );
  }

  /// Create manufacturing task order
  Future<void> _onCreateManufacturingTaskOrderRequested(
    CreateManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _createManufacturingTaskOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderCreated(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to create manufacturing task order'));
        }
      },
    );
  }

  /// Update manufacturing task order
  Future<void> _onUpdateManufacturingTaskOrderRequested(
    UpdateManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _updateManufacturingTaskOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderUpdated(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to update manufacturing task order'));
        }
      },
    );
  }

  /// Delete manufacturing task order
  Future<void> _onDeleteManufacturingTaskOrderRequested(
    DeleteManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _deleteManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success) {
          emit(ManufacturingTaskOrderDeleted(orderId: event.orderId));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to delete manufacturing task order'));
        }
      },
    );
  }

  /// Start manufacturing task order
  Future<void> _onStartManufacturingTaskOrderRequested(
    StartManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _startManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderStarted(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to start manufacturing task order'));
        }
      },
    );
  }

  /// Pause manufacturing task order
  Future<void> _onPauseManufacturingTaskOrderRequested(
    PauseManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _pauseManufacturingTaskOrderUseCase(event.request);

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderPaused(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to pause manufacturing task order'));
        }
      },
    );
  }

  /// Resume manufacturing task order
  Future<void> _onResumeManufacturingTaskOrderRequested(
    ResumeManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _resumeManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderResumed(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to resume manufacturing task order'));
        }
      },
    );
  }

  /// Complete manufacturing task order
  Future<void> _onCompleteManufacturingTaskOrderRequested(
    CompleteManufacturingTaskOrderRequested event,
    Emitter<ManufacturingTaskState> emit,
  ) async {
    emit(const ManufacturingTaskLoading());

    final result = await _completeManufacturingTaskOrderUseCase(IdParams(event.orderId));

    result.fold(
      (failure) => emit(ManufacturingTaskError(failure.message)),
      (response) {
        if (response.success && response.data != null) {
          emit(ManufacturingTaskOrderCompleted(order: response.data!));
        } else {
          emit(ManufacturingTaskError(response.message ?? 'Failed to complete manufacturing task order'));
        }
      },
    );
  }
