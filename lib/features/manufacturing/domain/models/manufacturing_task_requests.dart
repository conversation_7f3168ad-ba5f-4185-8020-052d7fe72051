import 'package:equatable/equatable.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../../resource_management/domain/entities/facility_entities.dart';
import '../entities/manufacturing_task_entities.dart';

// Manufacturing Task Order Requests

/// Request to create a manufacturing task order
class CreateManufacturingTaskOrderRequest extends Equatable {
  final String productionOrderId;
  final String productId;
  final String productName;
  final String clientName;
  final int totalQuantity;
  final ManufacturingOrderPriority priority;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final String? assignedSupervisor;
  final Map<String, dynamic> specifications;
  final List<ManufacturingStage> stages;
  final String? notes;
  final Map<String, dynamic>? customFields;

  const CreateManufacturingTaskOrderRequest({
    required this.productionOrderId,
    required this.productId,
    required this.productName,
    required this.clientName,
    required this.totalQuantity,
    required this.priority,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.assignedSupervisor,
    this.specifications = const {},
    required this.stages,
    this.notes,
    this.customFields,
  });

  @override
  List<Object?> get props => [
        productionOrderId,
        productId,
        productName,
        clientName,
        totalQuantity,
        priority,
        plannedStartDate,
        plannedEndDate,
        assignedSupervisor,
        specifications,
        stages,
        notes,
        customFields,
      ];
}

/// Request to update a manufacturing task order
class UpdateManufacturingTaskOrderRequest extends Equatable {
  final String id;
  final String? clientName;
  final ManufacturingOrderStatus? status;
  final ManufacturingOrderPriority? priority;
  final DateTime? plannedStartDate;
  final DateTime? plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final String? assignedSupervisor;
  final ManufacturingStage? currentStage;
  final int? completedQuantity;
  final Map<String, dynamic>? specifications;
  final Map<String, dynamic>? metadata;

  const UpdateManufacturingTaskOrderRequest({
    required this.id,
    this.clientName,
    this.status,
    this.priority,
    this.plannedStartDate,
    this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.assignedSupervisor,
    this.currentStage,
    this.completedQuantity,
    this.specifications,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        clientName,
        status,
        priority,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        assignedSupervisor,
        currentStage,
        completedQuantity,
        specifications,
        metadata,
      ];
}

/// Request to pause a manufacturing task order
class PauseManufacturingTaskOrderRequest extends Equatable {
  final String orderId;
  final String reason;
  final String pausedBy;

  const PauseManufacturingTaskOrderRequest({
    required this.orderId,
    required this.reason,
    required this.pausedBy,
  });

  @override
  List<Object?> get props => [orderId, reason, pausedBy];
}

/// Filter for manufacturing task orders
class ManufacturingTaskOrderFilter extends Equatable {
  final List<ManufacturingOrderStatus>? statuses;
  final List<ManufacturingOrderPriority>? priorities;
  final List<ManufacturingStage>? currentStages;
  final List<DepartmentType>? departments;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? clientName;
  final String? productId;
  final String? assignedSupervisor;
  final String? searchQuery;
  final bool? isOverdue;

  const ManufacturingTaskOrderFilter({
    this.statuses,
    this.priorities,
    this.currentStages,
    this.departments,
    this.startDate,
    this.endDate,
    this.clientName,
    this.productId,
    this.assignedSupervisor,
    this.searchQuery,
    this.isOverdue,
  });

  @override
  List<Object?> get props => [
        statuses,
        priorities,
        currentStages,
        departments,
        startDate,
        endDate,
        clientName,
        productId,
        assignedSupervisor,
        searchQuery,
        isOverdue,
      ];

  /// Check if filter is empty
  bool get isEmpty {
    return statuses == null &&
        priorities == null &&
        currentStages == null &&
        departments == null &&
        startDate == null &&
        endDate == null &&
        clientName == null &&
        productId == null &&
        assignedSupervisor == null &&
        (searchQuery == null || searchQuery!.isEmpty) &&
        isOverdue == null;
  }
}

// Stage Task Requests

/// Request to get stage tasks for an order
class GetStageTasksRequest extends Equatable {
  final String orderId;
  final ManufacturingStage? stage;
  final TaskStatus? status;
  final PaginationParams? pagination;

  const GetStageTasksRequest({
    required this.orderId,
    this.stage,
    this.status,
    this.pagination,
  });

  @override
  List<Object?> get props => [orderId, stage, status, pagination];
}

/// Request to get stage tasks by department
class GetStageTasksByDepartmentRequest extends Equatable {
  final DepartmentType department;
  final TaskStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final PaginationParams? pagination;

  const GetStageTasksByDepartmentRequest({
    required this.department,
    this.status,
    this.startDate,
    this.endDate,
    this.pagination,
  });

  @override
  List<Object?> get props => [department, status, startDate, endDate, pagination];
}

/// Request to get stage tasks by stage
class GetStageTasksByStageRequest extends Equatable {
  final ManufacturingStage stage;
  final TaskStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final PaginationParams? pagination;

  const GetStageTasksByStageRequest({
    required this.stage,
    this.status,
    this.startDate,
    this.endDate,
    this.pagination,
  });

  @override
  List<Object?> get props => [stage, status, startDate, endDate, pagination];
}

/// Request to create a stage task
class CreateStageTaskRequest extends Equatable {
  final String manufacturingTaskOrderId;
  final ManufacturingStage stage;
  final TaskType taskType;
  final String taskName;
  final String description;
  final TaskPriority priority;
  final DateTime plannedStartDate;
  final DateTime plannedEndDate;
  final List<String> requiredSkills;
  final int estimatedHours;
  final int quantityToProcess;
  final Map<String, dynamic> specifications;

  const CreateStageTaskRequest({
    required this.manufacturingTaskOrderId,
    required this.stage,
    required this.taskType,
    required this.taskName,
    required this.description,
    required this.priority,
    required this.plannedStartDate,
    required this.plannedEndDate,
    this.requiredSkills = const [],
    required this.estimatedHours,
    required this.quantityToProcess,
    this.specifications = const {},
  });

  @override
  List<Object?> get props => [
        manufacturingTaskOrderId,
        stage,
        taskType,
        taskName,
        description,
        priority,
        plannedStartDate,
        plannedEndDate,
        requiredSkills,
        estimatedHours,
        quantityToProcess,
        specifications,
      ];
}

/// Request to update a stage task
class UpdateStageTaskRequest extends Equatable {
  final String id;
  final String? taskName;
  final String? description;
  final TaskStatus? status;
  final TaskPriority? priority;
  final DateTime? plannedStartDate;
  final DateTime? plannedEndDate;
  final DateTime? actualStartDate;
  final DateTime? actualEndDate;
  final List<String>? assignedWorkers;
  final List<String>? requiredSkills;
  final int? estimatedHours;
  final int? actualHours;
  final int? quantityProcessed;
  final double? qualityScore;
  final String? qualityFeedback;
  final Map<String, dynamic>? specifications;

  const UpdateStageTaskRequest({
    required this.id,
    this.taskName,
    this.description,
    this.status,
    this.priority,
    this.plannedStartDate,
    this.plannedEndDate,
    this.actualStartDate,
    this.actualEndDate,
    this.assignedWorkers,
    this.requiredSkills,
    this.estimatedHours,
    this.actualHours,
    this.quantityProcessed,
    this.qualityScore,
    this.qualityFeedback,
    this.specifications,
  });

  @override
  List<Object?> get props => [
        id,
        taskName,
        description,
        status,
        priority,
        plannedStartDate,
        plannedEndDate,
        actualStartDate,
        actualEndDate,
        assignedWorkers,
        requiredSkills,
        estimatedHours,
        actualHours,
        quantityProcessed,
        qualityScore,
        qualityFeedback,
        specifications,
      ];
}

/// Request to complete a stage task
class CompleteStageTaskRequest extends Equatable {
  final String taskId;
  final int quantityProcessed;
  final double qualityScore;
  final String? qualityFeedback;
  final int actualHours;
  final String completedBy;

  const CompleteStageTaskRequest({
    required this.taskId,
    required this.quantityProcessed,
    required this.qualityScore,
    this.qualityFeedback,
    required this.actualHours,
    required this.completedBy,
  });

  @override
  List<Object?> get props => [
        taskId,
        quantityProcessed,
        qualityScore,
        qualityFeedback,
        actualHours,
        completedBy,
      ];
}

/// Request to assign workers to a stage task
class AssignWorkersToStageTaskRequest extends Equatable {
  final String taskId;
  final List<String> workerIds;
  final String assignedBy;

  const AssignWorkersToStageTaskRequest({
    required this.taskId,
    required this.workerIds,
    required this.assignedBy,
  });

  @override
  List<Object?> get props => [taskId, workerIds, assignedBy];
}

/// Request to update stage task progress
class UpdateStageTaskProgressRequest extends Equatable {
  final String taskId;
  final int quantityProcessed;
  final int actualHours;
  final double? qualityScore;
  final String? progressNotes;
  final String updatedBy;

  const UpdateStageTaskProgressRequest({
    required this.taskId,
    required this.quantityProcessed,
    required this.actualHours,
    this.qualityScore,
    this.progressNotes,
    required this.updatedBy,
  });

  @override
  List<Object?> get props => [
        taskId,
        quantityProcessed,
        actualHours,
        qualityScore,
        progressNotes,
        updatedBy,
      ];
}

// Stage Transfer Requests

/// Request to create a stage transfer
class CreateStageTransferRequest extends Equatable {
  final String manufacturingTaskOrderId;
  final String stageTaskId;
  final ManufacturingStage fromStage;
  final ManufacturingStage toStage;
  final int quantityTransferred;
  final String transferredBy;
  final String transferredByName;
  final String? notes;

  const CreateStageTransferRequest({
    required this.manufacturingTaskOrderId,
    required this.stageTaskId,
    required this.fromStage,
    required this.toStage,
    required this.quantityTransferred,
    required this.transferredBy,
    required this.transferredByName,
    this.notes,
  });

  @override
  List<Object?> get props => [
        manufacturingTaskOrderId,
        stageTaskId,
        fromStage,
        toStage,
        quantityTransferred,
        transferredBy,
        transferredByName,
        notes,
      ];
}

/// Request to get pending transfers for a department
class GetPendingTransfersRequest extends Equatable {
  final DepartmentType department;
  final DateTime? startDate;
  final DateTime? endDate;
  final PaginationParams? pagination;

  const GetPendingTransfersRequest({
    required this.department,
    this.startDate,
    this.endDate,
    this.pagination,
  });

  @override
  List<Object?> get props => [department, startDate, endDate, pagination];
}

/// Request to accept a stage transfer
class AcceptStageTransferRequest extends Equatable {
  final String transferId;
  final String receivedBy;
  final String receivedByName;
  final String? notes;

  const AcceptStageTransferRequest({
    required this.transferId,
    required this.receivedBy,
    required this.receivedByName,
    this.notes,
  });

  @override
  List<Object?> get props => [transferId, receivedBy, receivedByName, notes];
}

/// Request to reject a stage transfer
class RejectStageTransferRequest extends Equatable {
  final String transferId;
  final String rejectedBy;
  final String rejectedByName;
  final String reason;

  const RejectStageTransferRequest({
    required this.transferId,
    required this.rejectedBy,
    required this.rejectedByName,
    required this.reason,
  });

  @override
  List<Object?> get props => [transferId, rejectedBy, rejectedByName, reason];
}

// Note Requests

/// Request to add a note to an order
class AddNoteToOrderRequest extends Equatable {
  final String orderId;
  final String content;
  final NoteType type;
  final NotePriority priority;
  final bool isInternal;
  final String userId;
  final String userName;

  const AddNoteToOrderRequest({
    required this.orderId,
    required this.content,
    required this.type,
    required this.priority,
    this.isInternal = false,
    required this.userId,
    required this.userName,
  });

  @override
  List<Object?> get props => [
        orderId,
        content,
        type,
        priority,
        isInternal,
        userId,
        userName,
      ];
}

/// Request to add a note to a stage task
class AddNoteToStageTaskRequest extends Equatable {
  final String stageTaskId;
  final String content;
  final NoteType type;
  final NotePriority priority;
  final bool isInternal;
  final String userId;
  final String userName;

  const AddNoteToStageTaskRequest({
    required this.stageTaskId,
    required this.content,
    required this.type,
    required this.priority,
    this.isInternal = false,
    required this.userId,
    required this.userName,
  });

  @override
  List<Object?> get props => [
        stageTaskId,
        content,
        type,
        priority,
        isInternal,
        userId,
        userName,
      ];
}

/// Request to update a note
class UpdateNoteRequest extends Equatable {
  final String noteId;
  final String content;
  final NoteType? type;
  final NotePriority? priority;
  final bool? isInternal;

  const UpdateNoteRequest({
    required this.noteId,
    required this.content,
    this.type,
    this.priority,
    this.isInternal,
  });

  @override
  List<Object?> get props => [noteId, content, type, priority, isInternal];
}

// Analytics and Reporting Requests

/// Request to get manufacturing task analytics
class GetManufacturingTaskAnalyticsRequest extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final List<DepartmentType>? departments;
  final List<ManufacturingStage>? stages;
  final List<ManufacturingOrderPriority>? priorities;

  const GetManufacturingTaskAnalyticsRequest({
    required this.startDate,
    required this.endDate,
    this.departments,
    this.stages,
    this.priorities,
  });

  @override
  List<Object?> get props => [startDate, endDate, departments, stages, priorities];
}

/// Request to get stage performance metrics
class GetStagePerformanceMetricsRequest extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final List<ManufacturingStage>? stages;
  final List<DepartmentType>? departments;

  const GetStagePerformanceMetricsRequest({
    required this.startDate,
    required this.endDate,
    this.stages,
    this.departments,
  });

  @override
  List<Object?> get props => [startDate, endDate, stages, departments];
}

/// Request to get bottleneck analysis
class GetBottleneckAnalysisRequest extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final List<DepartmentType>? departments;

  const GetBottleneckAnalysisRequest({
    required this.startDate,
    required this.endDate,
    this.departments,
  });

  @override
  List<Object?> get props => [startDate, endDate, departments];
}

/// Request to get efficiency metrics
class GetEfficiencyMetricsRequest extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final List<DepartmentType>? departments;
  final List<ManufacturingStage>? stages;

  const GetEfficiencyMetricsRequest({
    required this.startDate,
    required this.endDate,
    this.departments,
    this.stages,
  });

  @override
  List<Object?> get props => [startDate, endDate, departments, stages];
}

// Search and Filter Requests

/// Request to search manufacturing task orders
class SearchManufacturingTaskOrdersRequest extends Equatable {
  final String query;
  final ManufacturingTaskOrderFilter? filter;
  final PaginationParams? pagination;

  const SearchManufacturingTaskOrdersRequest({
    required this.query,
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [query, filter, pagination];
}

/// Request to get overdue manufacturing task orders
class GetOverdueManufacturingTaskOrdersRequest extends Equatable {
  final List<DepartmentType>? departments;
  final List<ManufacturingOrderPriority>? priorities;
  final PaginationParams? pagination;

  const GetOverdueManufacturingTaskOrdersRequest({
    this.departments,
    this.priorities,
    this.pagination,
  });

  @override
  List<Object?> get props => [departments, priorities, pagination];
}

/// Request to get urgent manufacturing task orders
class GetUrgentManufacturingTaskOrdersRequest extends Equatable {
  final List<DepartmentType>? departments;
  final PaginationParams? pagination;

  const GetUrgentManufacturingTaskOrdersRequest({
    this.departments,
    this.pagination,
  });

  @override
  List<Object?> get props => [departments, pagination];
}

/// Request to get manufacturing task orders by status
class GetManufacturingTaskOrdersByStatusRequest extends Equatable {
  final List<ManufacturingOrderStatus> statuses;
  final List<DepartmentType>? departments;
  final PaginationParams? pagination;

  const GetManufacturingTaskOrdersByStatusRequest({
    required this.statuses,
    this.departments,
    this.pagination,
  });

  @override
  List<Object?> get props => [statuses, departments, pagination];
}

/// Request to get stage tasks by worker
class GetStageTasksByWorkerRequest extends Equatable {
  final String workerId;
  final List<TaskStatus>? statuses;
  final DateTime? startDate;
  final DateTime? endDate;
  final PaginationParams? pagination;

  const GetStageTasksByWorkerRequest({
    required this.workerId,
    this.statuses,
    this.startDate,
    this.endDate,
    this.pagination,
  });

  @override
  List<Object?> get props => [workerId, statuses, startDate, endDate, pagination];
}

/// Request to get stage tasks by status
class GetStageTasksByStatusRequest extends Equatable {
  final List<TaskStatus> statuses;
  final List<DepartmentType>? departments;
  final List<ManufacturingStage>? stages;
  final DateTime? startDate;
  final DateTime? endDate;
  final PaginationParams? pagination;

  const GetStageTasksByStatusRequest({
    required this.statuses,
    this.departments,
    this.stages,
    this.startDate,
    this.endDate,
    this.pagination,
  });

  @override
  List<Object?> get props => [statuses, departments, stages, startDate, endDate, pagination];
}
