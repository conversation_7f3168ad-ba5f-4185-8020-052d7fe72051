import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/manufacturing_task_entities.dart';
import '../entities/manufacturing_task_analytics.dart';
import '../models/manufacturing_task_requests.dart';
import '../repositories/manufacturing_task_repository.dart';

// Manufacturing Task Order Use Cases

/// Get Manufacturing Task Orders Use Case
class GetManufacturingTaskOrdersUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskOrder>, GetManufacturingTaskOrdersParams> {
  final ManufacturingTaskRepository repository;

  GetManufacturingTaskOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> call(
      GetManufacturingTaskOrdersParams params) async {
    return await repository.getManufacturingTaskOrders(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

class GetManufacturingTaskOrdersParams extends Equatable {
  final ManufacturingTaskOrderFilter? filter;
  final PaginationParams? pagination;

  const GetManufacturingTaskOrdersParams({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Get Manufacturing Task Order By ID Use Case
class GetManufacturingTaskOrderByIdUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  GetManufacturingTaskOrderByIdUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.getManufacturingTaskOrderById(params.id);
  }
}

/// Create Manufacturing Task Order Use Case
class CreateManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, CreateManufacturingTaskOrderRequest> {
  final ManufacturingTaskRepository repository;

  CreateManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      CreateManufacturingTaskOrderRequest params) async {
    return await repository.createManufacturingTaskOrder(params);
  }
}

/// Update Manufacturing Task Order Use Case
class UpdateManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, UpdateManufacturingTaskOrderRequest> {
  final ManufacturingTaskRepository repository;

  UpdateManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      UpdateManufacturingTaskOrderRequest params) async {
    return await repository.updateManufacturingTaskOrder(params);
  }
}

/// Delete Manufacturing Task Order Use Case
class DeleteManufacturingTaskOrderUseCase
    implements UseCase<ApiVoidResponse, IdParams> {
  final ManufacturingTaskRepository repository;

  DeleteManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await repository.deleteManufacturingTaskOrder(params.id);
  }
}

/// Start Manufacturing Task Order Use Case
class StartManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  StartManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.startManufacturingTaskOrder(params.id);
  }
}

/// Pause Manufacturing Task Order Use Case
class PauseManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, PauseManufacturingTaskOrderRequest> {
  final ManufacturingTaskRepository repository;

  PauseManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      PauseManufacturingTaskOrderRequest params) async {
    return await repository.pauseManufacturingTaskOrder(params);
  }
}

/// Resume Manufacturing Task Order Use Case
class ResumeManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  ResumeManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.resumeManufacturingTaskOrder(params.id);
  }
}

/// Complete Manufacturing Task Order Use Case
class CompleteManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  CompleteManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.completeManufacturingTaskOrder(params.id);
  }
}

// Stage Task Use Cases

/// Get Stage Tasks For Order Use Case
class GetStageTasksForOrderUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksForOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksRequest params) async {
    return await repository.getStageTasksForOrder(params);
  }
}

/// Get Stage Tasks By Department Use Case
class GetStageTasksByDepartmentUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksByDepartmentRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksByDepartmentUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksByDepartmentRequest params) async {
    return await repository.getStageTasksByDepartment(params);
  }
}

/// Get Stage Tasks By Stage Use Case
class GetStageTasksByStageUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksByStageRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksByStageUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksByStageRequest params) async {
    return await repository.getStageTasksByStage(params);
  }
}

/// Get Stage Task By ID Use Case
class GetStageTaskByIdUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, IdParams> {
  final ManufacturingTaskRepository repository;

  GetStageTaskByIdUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      IdParams params) async {
    return await repository.getStageTaskById(params.id);
  }
}

/// Create Stage Task Use Case
class CreateStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, CreateStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  CreateStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      CreateStageTaskRequest params) async {
    return await repository.createStageTask(params);
  }
}

/// Update Stage Task Use Case
class UpdateStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, UpdateStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  UpdateStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      UpdateStageTaskRequest params) async {
    return await repository.updateStageTask(params);
  }
}

/// Start Stage Task Use Case
class StartStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, IdParams> {
  final ManufacturingTaskRepository repository;

  StartStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      IdParams params) async {
    return await repository.startStageTask(params.id);
  }
}

/// Complete Stage Task Use Case
class CompleteStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, CompleteStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  CompleteStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      CompleteStageTaskRequest params) async {
    return await repository.completeStageTask(params);
  }
}

/// Assign Workers To Stage Task Use Case
class AssignWorkersToStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, AssignWorkersToStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  AssignWorkersToStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      AssignWorkersToStageTaskRequest params) async {
    return await repository.assignWorkersToStageTask(params);
  }
}

/// Update Stage Task Progress Use Case
class UpdateStageTaskProgressUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, UpdateStageTaskProgressRequest> {
  final ManufacturingTaskRepository repository;

  UpdateStageTaskProgressUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      UpdateStageTaskProgressRequest params) async {
    return await repository.updateStageTaskProgress(params);
  }
}
