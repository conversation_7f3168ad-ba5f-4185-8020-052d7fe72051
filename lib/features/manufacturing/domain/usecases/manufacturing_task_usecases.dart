import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../shared/models/api_response.dart';
import '../../../../shared/models/pagination.dart';
import '../entities/manufacturing_task_entities.dart';
import '../entities/manufacturing_task_analytics.dart';
import '../models/manufacturing_task_requests.dart';
import '../repositories/manufacturing_task_repository.dart';

// Manufacturing Task Order Use Cases

/// Get Manufacturing Task Orders Use Case
class GetManufacturingTaskOrdersUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskOrder>, GetManufacturingTaskOrdersParams> {
  final ManufacturingTaskRepository repository;

  GetManufacturingTaskOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> call(
      GetManufacturingTaskOrdersParams params) async {
    return await repository.getManufacturingTaskOrders(
      filter: params.filter,
      pagination: params.pagination,
    );
  }
}

class GetManufacturingTaskOrdersParams extends Equatable {
  final ManufacturingTaskOrderFilter? filter;
  final PaginationParams? pagination;

  const GetManufacturingTaskOrdersParams({
    this.filter,
    this.pagination,
  });

  @override
  List<Object?> get props => [filter, pagination];
}

/// Get Manufacturing Task Order By ID Use Case
class GetManufacturingTaskOrderByIdUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  GetManufacturingTaskOrderByIdUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.getManufacturingTaskOrderById(params.id);
  }
}

/// Create Manufacturing Task Order Use Case
class CreateManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, CreateManufacturingTaskOrderRequest> {
  final ManufacturingTaskRepository repository;

  CreateManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      CreateManufacturingTaskOrderRequest params) async {
    return await repository.createManufacturingTaskOrder(params);
  }
}

/// Update Manufacturing Task Order Use Case
class UpdateManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, UpdateManufacturingTaskOrderRequest> {
  final ManufacturingTaskRepository repository;

  UpdateManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      UpdateManufacturingTaskOrderRequest params) async {
    return await repository.updateManufacturingTaskOrder(params);
  }
}

/// Delete Manufacturing Task Order Use Case
class DeleteManufacturingTaskOrderUseCase
    implements UseCase<ApiVoidResponse, IdParams> {
  final ManufacturingTaskRepository repository;

  DeleteManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await repository.deleteManufacturingTaskOrder(params.id);
  }
}

/// Start Manufacturing Task Order Use Case
class StartManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  StartManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.startManufacturingTaskOrder(params.id);
  }
}

/// Pause Manufacturing Task Order Use Case
class PauseManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, PauseManufacturingTaskOrderRequest> {
  final ManufacturingTaskRepository repository;

  PauseManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      PauseManufacturingTaskOrderRequest params) async {
    return await repository.pauseManufacturingTaskOrder(params);
  }
}

/// Resume Manufacturing Task Order Use Case
class ResumeManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  ResumeManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.resumeManufacturingTaskOrder(params.id);
  }
}

/// Complete Manufacturing Task Order Use Case
class CompleteManufacturingTaskOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskOrder>, IdParams> {
  final ManufacturingTaskRepository repository;

  CompleteManufacturingTaskOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskOrder>>> call(
      IdParams params) async {
    return await repository.completeManufacturingTaskOrder(params.id);
  }
}

// Stage Task Use Cases

/// Get Stage Tasks For Order Use Case
class GetStageTasksForOrderUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksForOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksRequest params) async {
    return await repository.getStageTasksForOrder(params);
  }
}

/// Get Stage Tasks By Department Use Case
class GetStageTasksByDepartmentUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksByDepartmentRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksByDepartmentUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksByDepartmentRequest params) async {
    return await repository.getStageTasksByDepartment(params);
  }
}

/// Get Stage Tasks By Stage Use Case
class GetStageTasksByStageUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksByStageRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksByStageUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksByStageRequest params) async {
    return await repository.getStageTasksByStage(params);
  }
}

/// Get Stage Task By ID Use Case
class GetStageTaskByIdUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, IdParams> {
  final ManufacturingTaskRepository repository;

  GetStageTaskByIdUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      IdParams params) async {
    return await repository.getStageTaskById(params.id);
  }
}

/// Create Stage Task Use Case
class CreateStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, CreateStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  CreateStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      CreateStageTaskRequest params) async {
    return await repository.createStageTask(params);
  }
}

/// Update Stage Task Use Case
class UpdateStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, UpdateStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  UpdateStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      UpdateStageTaskRequest params) async {
    return await repository.updateStageTask(params);
  }
}

/// Start Stage Task Use Case
class StartStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, IdParams> {
  final ManufacturingTaskRepository repository;

  StartStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      IdParams params) async {
    return await repository.startStageTask(params.id);
  }
}

/// Complete Stage Task Use Case
class CompleteStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, CompleteStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  CompleteStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      CompleteStageTaskRequest params) async {
    return await repository.completeStageTask(params);
  }
}

/// Assign Workers To Stage Task Use Case
class AssignWorkersToStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, AssignWorkersToStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  AssignWorkersToStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      AssignWorkersToStageTaskRequest params) async {
    return await repository.assignWorkersToStageTask(params);
  }
}

/// Update Stage Task Progress Use Case
class UpdateStageTaskProgressUseCase
    implements UseCase<ApiResponse<ManufacturingStageTask>, UpdateStageTaskProgressRequest> {
  final ManufacturingTaskRepository repository;

  UpdateStageTaskProgressUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTask>>> call(
      UpdateStageTaskProgressRequest params) async {
    return await repository.updateStageTaskProgress(params);
  }
}

// Stage Transfer Use Cases

/// Create Stage Transfer Use Case
class CreateStageTransferUseCase
    implements UseCase<ApiResponse<ManufacturingStageTransfer>, CreateStageTransferRequest> {
  final ManufacturingTaskRepository repository;

  CreateStageTransferUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> call(
      CreateStageTransferRequest params) async {
    return await repository.createStageTransfer(params);
  }
}

/// Get Transfers For Order Use Case
class GetTransfersForOrderUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTransfer>, IdParams> {
  final ManufacturingTaskRepository repository;

  GetTransfersForOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTransfer>>> call(
      IdParams params) async {
    return await repository.getTransfersForOrder(params.id);
  }
}

/// Get Pending Transfers For Department Use Case
class GetPendingTransfersForDepartmentUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTransfer>, GetPendingTransfersRequest> {
  final ManufacturingTaskRepository repository;

  GetPendingTransfersForDepartmentUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTransfer>>> call(
      GetPendingTransfersRequest params) async {
    return await repository.getPendingTransfersForDepartment(params);
  }
}

/// Accept Stage Transfer Use Case
class AcceptStageTransferUseCase
    implements UseCase<ApiResponse<ManufacturingStageTransfer>, AcceptStageTransferRequest> {
  final ManufacturingTaskRepository repository;

  AcceptStageTransferUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> call(
      AcceptStageTransferRequest params) async {
    return await repository.acceptStageTransfer(params);
  }
}

/// Reject Stage Transfer Use Case
class RejectStageTransferUseCase
    implements UseCase<ApiResponse<ManufacturingStageTransfer>, RejectStageTransferRequest> {
  final ManufacturingTaskRepository repository;

  RejectStageTransferUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> call(
      RejectStageTransferRequest params) async {
    return await repository.rejectStageTransfer(params);
  }
}

/// Complete Stage Transfer Use Case
class CompleteStageTransferUseCase
    implements UseCase<ApiResponse<ManufacturingStageTransfer>, IdParams> {
  final ManufacturingTaskRepository repository;

  CompleteStageTransferUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingStageTransfer>>> call(
      IdParams params) async {
    return await repository.completeStageTransfer(params.id);
  }
}

// Notes Use Cases

/// Add Note To Order Use Case
class AddNoteToOrderUseCase
    implements UseCase<ApiResponse<ManufacturingTaskNote>, AddNoteToOrderRequest> {
  final ManufacturingTaskRepository repository;

  AddNoteToOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskNote>>> call(
      AddNoteToOrderRequest params) async {
    return await repository.addNoteToOrder(params);
  }
}

/// Add Note To Stage Task Use Case
class AddNoteToStageTaskUseCase
    implements UseCase<ApiResponse<ManufacturingTaskNote>, AddNoteToStageTaskRequest> {
  final ManufacturingTaskRepository repository;

  AddNoteToStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskNote>>> call(
      AddNoteToStageTaskRequest params) async {
    return await repository.addNoteToStageTask(params);
  }
}

/// Get Notes For Order Use Case
class GetNotesForOrderUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskNote>, IdParams> {
  final ManufacturingTaskRepository repository;

  GetNotesForOrderUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskNote>>> call(
      IdParams params) async {
    return await repository.getNotesForOrder(params.id);
  }
}

/// Get Notes For Stage Task Use Case
class GetNotesForStageTaskUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskNote>, IdParams> {
  final ManufacturingTaskRepository repository;

  GetNotesForStageTaskUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskNote>>> call(
      IdParams params) async {
    return await repository.getNotesForStageTask(params.id);
  }
}

/// Update Note Use Case
class UpdateNoteUseCase
    implements UseCase<ApiResponse<ManufacturingTaskNote>, UpdateNoteRequest> {
  final ManufacturingTaskRepository repository;

  UpdateNoteUseCase(this.repository);

  @override
  Future<Either<Failure, ApiResponse<ManufacturingTaskNote>>> call(
      UpdateNoteRequest params) async {
    return await repository.updateNote(params);
  }
}

/// Delete Note Use Case
class DeleteNoteUseCase
    implements UseCase<ApiVoidResponse, IdParams> {
  final ManufacturingTaskRepository repository;

  DeleteNoteUseCase(this.repository);

  @override
  Future<Either<Failure, ApiVoidResponse>> call(IdParams params) async {
    return await repository.deleteNote(params.id);
  }
}

// Analytics Use Cases

/// Get Manufacturing Task Analytics Use Case
class GetManufacturingTaskAnalyticsUseCase
    implements UseCase<ManufacturingTaskAnalytics, GetManufacturingTaskAnalyticsRequest> {
  final ManufacturingTaskRepository repository;

  GetManufacturingTaskAnalyticsUseCase(this.repository);

  @override
  Future<Either<Failure, ManufacturingTaskAnalytics>> call(
      GetManufacturingTaskAnalyticsRequest params) async {
    return await repository.getManufacturingTaskAnalytics(params);
  }
}

/// Get Stage Performance Metrics Use Case
class GetStagePerformanceMetricsUseCase
    implements UseCase<List<StagePerformanceMetrics>, GetStagePerformanceMetricsRequest> {
  final ManufacturingTaskRepository repository;

  GetStagePerformanceMetricsUseCase(this.repository);

  @override
  Future<Either<Failure, List<StagePerformanceMetrics>>> call(
      GetStagePerformanceMetricsRequest params) async {
    return await repository.getStagePerformanceMetrics(params);
  }
}

/// Get Bottleneck Analysis Use Case
class GetBottleneckAnalysisUseCase
    implements UseCase<List<ManufacturingBottleneck>, GetBottleneckAnalysisRequest> {
  final ManufacturingTaskRepository repository;

  GetBottleneckAnalysisUseCase(this.repository);

  @override
  Future<Either<Failure, List<ManufacturingBottleneck>>> call(
      GetBottleneckAnalysisRequest params) async {
    return await repository.getBottleneckAnalysis(params);
  }
}

/// Get Efficiency Metrics Use Case
class GetEfficiencyMetricsUseCase
    implements UseCase<ManufacturingEfficiencyMetrics, GetEfficiencyMetricsRequest> {
  final ManufacturingTaskRepository repository;

  GetEfficiencyMetricsUseCase(this.repository);

  @override
  Future<Either<Failure, ManufacturingEfficiencyMetrics>> call(
      GetEfficiencyMetricsRequest params) async {
    return await repository.getEfficiencyMetrics(params);
  }
}

// Search and Filter Use Cases

/// Search Manufacturing Task Orders Use Case
class SearchManufacturingTaskOrdersUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskOrder>, SearchManufacturingTaskOrdersRequest> {
  final ManufacturingTaskRepository repository;

  SearchManufacturingTaskOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> call(
      SearchManufacturingTaskOrdersRequest params) async {
    return await repository.searchManufacturingTaskOrders(params);
  }
}

/// Get Overdue Manufacturing Task Orders Use Case
class GetOverdueManufacturingTaskOrdersUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskOrder>, GetOverdueManufacturingTaskOrdersRequest> {
  final ManufacturingTaskRepository repository;

  GetOverdueManufacturingTaskOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> call(
      GetOverdueManufacturingTaskOrdersRequest params) async {
    return await repository.getOverdueManufacturingTaskOrders(params);
  }
}

/// Get Urgent Manufacturing Task Orders Use Case
class GetUrgentManufacturingTaskOrdersUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskOrder>, GetUrgentManufacturingTaskOrdersRequest> {
  final ManufacturingTaskRepository repository;

  GetUrgentManufacturingTaskOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> call(
      GetUrgentManufacturingTaskOrdersRequest params) async {
    return await repository.getUrgentManufacturingTaskOrders(params);
  }
}

/// Get Manufacturing Task Orders By Status Use Case
class GetManufacturingTaskOrdersByStatusUseCase
    implements UseCase<ApiListResponse<ManufacturingTaskOrder>, GetManufacturingTaskOrdersByStatusRequest> {
  final ManufacturingTaskRepository repository;

  GetManufacturingTaskOrdersByStatusUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingTaskOrder>>> call(
      GetManufacturingTaskOrdersByStatusRequest params) async {
    return await repository.getManufacturingTaskOrdersByStatus(params);
  }
}

/// Get Stage Tasks By Worker Use Case
class GetStageTasksByWorkerUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksByWorkerRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksByWorkerUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksByWorkerRequest params) async {
    return await repository.getStageTasksByWorker(params);
  }
}

/// Get Stage Tasks By Status Use Case
class GetStageTasksByStatusUseCase
    implements UseCase<ApiListResponse<ManufacturingStageTask>, GetStageTasksByStatusRequest> {
  final ManufacturingTaskRepository repository;

  GetStageTasksByStatusUseCase(this.repository);

  @override
  Future<Either<Failure, ApiListResponse<ManufacturingStageTask>>> call(
      GetStageTasksByStatusRequest params) async {
    return await repository.getStageTasksByStatus(params);
  }
}
