import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/loading_screen.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../../../dashboard/presentation/pages/cutting_master_dashboard_page.dart';
import '../../data/services/biometric_service.dart';
import '../bloc/firebase_auth_bloc.dart';
import '../widgets/loading_button.dart';

/// Cutting Master specialized login page
class CuttingMasterLoginPage extends StatefulWidget {
  const CuttingMasterLoginPage({super.key});

  @override
  State<CuttingMasterLoginPage> createState() => _CuttingMasterLoginPageState();
}

class _CuttingMasterLoginPageState extends State<CuttingMasterLoginPage>
    with TickerProviderStateMixin {
  // Controllers
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Form keys
  final _signInFormKey = GlobalKey<FormState>();
  final _signUpFormKey = GlobalKey<FormState>();

  // State variables
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _rememberMe = false;
  bool _acceptTerms = false;
  bool _isBiometricAvailable = false;
  int _selectedTabIndex = 0;

  // Services
  late BiometricService _biometricService;
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _biometricService = BiometricService();
    _tabController = TabController(length: 2, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _checkBiometricAvailability();
    _animationController.forward();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _confirmPasswordController.dispose();
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkBiometricAvailability() async {
    final isAvailable = await _biometricService.isBiometricAvailable();
    if (mounted) {
      setState(() {
        _isBiometricAvailable = isAvailable;
      });
    }
  }

  void _handleAuthStateChanges(BuildContext context, FirebaseAuthState state) {
    setState(() {
      _isLoading = state is FirebaseAuthLoading;
    });

    if (state is FirebaseAuthAuthenticated) {
      // Debug: Print user role for troubleshooting
      print('User authenticated with role: ${state.user.role}');

      // Check if user is cutting master
      if (state.user.role == UserRole.cuttingMaster) {
        _showSuccessSnackBar('Welcome back, Cutting Master!');
        // Add a small delay to ensure the snackbar is shown
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _navigateToCuttingMasterDashboard(context);
          }
        });
      } else {
        // For now, allow any user to access cutting master dashboard for testing
        // TODO: Remove this in production and uncomment the access control below
        _showSuccessSnackBar('Welcome! Redirecting to Cutting Master Dashboard...');
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _navigateToCuttingMasterDashboard(context);
          }
        });

        // Uncomment this for production with proper role checking:
        // _showErrorSnackBar('Access denied. This login is for Cutting Masters only.');
        // context.read<FirebaseAuthBloc>().add(const FirebaseSignOutRequested());
      }
    } else if (state is FirebaseAuthError) {
      _showErrorSnackBar(state.message);
    } else if (state is FirebaseAuthSignUpSuccess) {
      _showSuccessSnackBar('Account created successfully! Signing you in...');
    }
  }

  void _navigateToCuttingMasterDashboard(BuildContext context) {
    // Use GoRouter for better navigation management
    try {
      context.go('/cutting-master-dashboard');
    } catch (e) {
      // Fallback to Navigator if GoRouter fails
      print('GoRouter navigation failed, using Navigator: $e');
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => const CuttingMasterDashboardPage(),
        ),
        (route) => false, // Remove all previous routes
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'This field is required';
    }
    if (value.length < 2) {
      return 'Name must be at least 2 characters';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<FirebaseAuthBloc>(),
      child: Scaffold(
        body: BlocListener<FirebaseAuthBloc, FirebaseAuthState>(
          listener: _handleAuthStateChanges,
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF6A1B9A), // Deep Purple
                  Color(0xFF8E24AA), // Purple
                  Color(0xFFAB47BC), // Light Purple
                ],
              ),
            ),
            child: SafeArea(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      children: [
                        const SizedBox(height: 40),
                        _buildHeader(),
                        const SizedBox(height: 40),
                        _buildLoginCard(),
                        const SizedBox(height: 20),
                        _buildTestNavigationButton(),
                        const SizedBox(height: 12),
                        _buildBackToMainButton(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: const Icon(
            Icons.content_cut,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          'Cutting Master',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Fabric Cutting & Pattern Management',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildTabBar(),
          const SizedBox(height: 20),
          _buildTabBarView(),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.gray100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: const Color(0xFF6A1B9A),
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: const TextStyle(fontWeight: FontWeight.w600),
        onTap: (index) {
          setState(() {
            _selectedTabIndex = index;
          });
        },
        tabs: const [
          Tab(text: 'Sign In'),
          Tab(text: 'Sign Up'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return SizedBox(
      height: _selectedTabIndex == 0 ? 400 : 500,
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildSignInForm(),
          _buildSignUpForm(),
        ],
      ),
    );
  }

  Widget _buildSignInForm() {
    return Form(
      key: _signInFormKey,
      child: Column(
        children: [
          CustomTextField(
            controller: _emailController,
            label: 'Email',
            hintText: 'Enter your email',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email_outlined,
            validator: _validateEmail,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _passwordController,
            label: 'Password',
            hintText: 'Enter your password',
            obscureText: !_isPasswordVisible,
            prefixIcon: Icons.lock_outlined,
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
            ),
            validator: _validatePassword,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
                activeColor: const Color(0xFF6A1B9A),
              ),
              const Text('Remember me'),
            ],
          ),
          const SizedBox(height: 24),
          _buildSignInButton(),
          if (_isBiometricAvailable) ...[
            const SizedBox(height: 16),
            _buildBiometricButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildSignUpForm() {
    return Form(
      key: _signUpFormKey,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _firstNameController,
                  label: 'First Name',
                  hintText: 'Enter first name',
                  prefixIcon: Icons.person_outlined,
                  validator: _validateName,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomTextField(
                  controller: _lastNameController,
                  label: 'Last Name',
                  hintText: 'Enter last name',
                  prefixIcon: Icons.person_outlined,
                  validator: _validateName,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _emailController,
            label: 'Email',
            hintText: 'Enter your email',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email_outlined,
            validator: _validateEmail,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _passwordController,
            label: 'Password',
            hintText: 'Enter your password',
            obscureText: !_isPasswordVisible,
            prefixIcon: Icons.lock_outlined,
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
            ),
            validator: _validatePassword,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _confirmPasswordController,
            label: 'Confirm Password',
            hintText: 'Confirm your password',
            obscureText: !_isConfirmPasswordVisible,
            prefixIcon: Icons.lock_outlined,
            suffixIcon: IconButton(
              icon: Icon(
                _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                });
              },
            ),
            validator: _validateConfirmPassword,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Checkbox(
                value: _acceptTerms,
                onChanged: (value) {
                  setState(() {
                    _acceptTerms = value ?? false;
                  });
                },
                activeColor: const Color(0xFF6A1B9A),
              ),
              const Expanded(
                child: Text(
                  'I accept the Terms and Conditions',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSignUpButton(),
        ],
      ),
    );
  }

  Widget _buildSignInButton() {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: LoadingButton(
            onPressed: _isLoading ? null : _handleSignIn,
            isLoading: _isLoading,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6A1B9A),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Sign In as Cutting Master',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSignUpButton() {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: LoadingButton(
            onPressed: (_isLoading || !_acceptTerms) ? null : _handleSignUp,
            isLoading: _isLoading,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6A1B9A),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Create Cutting Master Account',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBiometricButton() {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: _isLoading ? null : _handleBiometricSignIn,
        icon: const Icon(Icons.fingerprint),
        label: const Text('Sign in with Biometrics'),
        style: OutlinedButton.styleFrom(
          foregroundColor: const Color(0xFF6A1B9A),
          side: const BorderSide(color: Color(0xFF6A1B9A)),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildTestNavigationButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          _showSuccessSnackBar('Testing navigation to Cutting Master Dashboard...');
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              _navigateToCuttingMasterDashboard(context);
            }
          });
        },
        icon: const Icon(Icons.dashboard, color: Colors.white),
        label: const Text(
          'Test Dashboard Navigation',
          style: TextStyle(color: Colors.white),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildBackToMainButton() {
    return TextButton.icon(
      onPressed: () {
        Navigator.of(context).pop();
      },
      icon: const Icon(Icons.arrow_back, color: Colors.white),
      label: const Text(
        'Back to Main Login',
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
      ),
    );
  }

  Future<void> _handleSignIn() async {
    if (!_signInFormKey.currentState!.validate()) return;

    print('Starting sign-in process...');
    setState(() {
      _isLoading = true;
    });

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    print('Signing in with email: $email');

    // Store credentials if remember me is enabled
    if (_rememberMe) {
      await _biometricService.storeCredentials(email, password);
    } else {
      await _biometricService.clearStoredCredentials();
    }

    context.read<FirebaseAuthBloc>().add(
      FirebaseSignInRequested(
        email: email,
        password: password,
      ),
    );
  }

  Future<void> _handleSignUp() async {
    if (!_signUpFormKey.currentState!.validate()) return;
    if (!_acceptTerms) {
      _showErrorSnackBar('Please accept the terms and conditions');
      return;
    }

    print('Starting sign-up process for cutting master...');
    setState(() {
      _isLoading = true;
    });

    final email = _emailController.text.trim();
    final firstName = _firstNameController.text.trim();
    final lastName = _lastNameController.text.trim();

    print('Creating account for: $firstName $lastName ($email) with role: ${UserRole.cuttingMaster}');

    context.read<FirebaseAuthBloc>().add(
      FirebaseSignUpRequested(
        email: email,
        password: _passwordController.text,
        firstName: firstName,
        lastName: lastName,
        role: UserRole.cuttingMaster, // Fixed role for cutting master
      ),
    );
  }

  Future<void> _handleBiometricSignIn() async {
    if (!_isBiometricAvailable) {
      _showErrorSnackBar('Biometric authentication is not available');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final credentials = await _biometricService.getStoredCredentials();
      if (credentials['email'] == null || credentials['password'] == null) {
        _showErrorSnackBar('No saved credentials found');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final isAuthenticated = await _biometricService.authenticateWithBiometrics();

      if (isAuthenticated && mounted) {
        setState(() {
          _emailController.text = credentials['email']!;
          _passwordController.text = credentials['password']!;
        });
        await _handleSignIn();
      } else if (mounted) {
        _showErrorSnackBar('Biometric authentication failed');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Biometric authentication error: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
}