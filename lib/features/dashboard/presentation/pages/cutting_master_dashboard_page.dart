import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Cutting Master Dashboard Page - Fabric cutting and marker planning
class CuttingMasterDashboardPage extends StatelessWidget {
  const CuttingMasterDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cutting Master Dashboard'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildCuttingMasterDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildCuttingMasterDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildWorkOverview(),
          const SizedBox(height: 24),
          _buildCuttingActions(context),
          const SizedBox(height: 24),
          _buildMyTasks(),
          const SizedBox(height: 24),
          _buildMarkerPlanning(),
          const SizedBox(height: 24),
          _buildFabricUtilization(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.deepPurple,
            Colors.deepPurple.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Cutting Master'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Fabric cutting and marker planning',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.design_services,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildWorkOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Today\'s Work Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: const [
            DashboardMetricCard(
              title: 'My Tasks',
              value: '6',
              icon: Icons.assignment,
              color: Colors.blue,
              trend: '+2',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Completed',
              value: '4',
              icon: Icons.check_circle,
              color: Colors.green,
              trend: '+1',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Fabric Efficiency',
              value: '94%',
              icon: Icons.trending_up,
              color: Colors.purple,
              trend: '+2%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Markers Created',
              value: '8',
              icon: Icons.straighten,
              color: Colors.orange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCuttingActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cutting Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 2.8,
          children: [
            DashboardCard(
              title: 'Create Marker',
              subtitle: 'Design cutting markers',
              icon: Icons.straighten,
              color: Colors.blue,
              onTap: () => _showCreateMarkerDialog(context),
            ),
            DashboardCard(
              title: 'Start Cutting',
              subtitle: 'Begin cutting process',
              icon: Icons.content_cut,
              color: Colors.green,
              onTap: () => _showStartCuttingDialog(context),
            ),
            DashboardCard(
              title: 'Fabric Calculator',
              subtitle: 'Calculate fabric requirements',
              icon: Icons.calculate,
              color: Colors.purple,
              onTap: () => _showFabricCalculatorDialog(context),
            ),
            DashboardCard(
              title: 'Quality Check',
              subtitle: 'Inspect cut pieces',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () => _showQualityCheckDialog(context),
            ),
            DashboardCard(
              title: 'Task Updates',
              subtitle: 'Update task progress',
              icon: Icons.update,
              color: Colors.teal,
              onTap: () => _showTaskUpdateDialog(context),
            ),
            DashboardCard(
              title: 'Production Log',
              subtitle: 'Log production data',
              icon: Icons.history,
              color: Colors.indigo,
              onTap: () => _showProductionLogDialog(context),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMyTasks() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.assignment,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'My Cutting Tasks',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to all tasks
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTaskItem(
              'CUT-001',
              'Shirt cutting - Order #1234',
              'High',
              'In Progress',
              Colors.blue,
              0.65,
            ),
            _buildTaskItem(
              'CUT-002',
              'Trouser cutting - Order #1235',
              'Medium',
              'Pending',
              Colors.orange,
              0.0,
            ),
            _buildTaskItem(
              'CUT-003',
              'Dress cutting - Order #1236',
              'High',
              'Completed',
              Colors.green,
              1.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskItem(String taskId, String description, String priority, String status, Color color, double progress) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      taskId,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getPriorityColor(priority).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  priority,
                  style: TextStyle(
                    color: _getPriorityColor(priority),
                    fontWeight: FontWeight.w500,
                    fontSize: 10,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget _buildMarkerPlanning() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.straighten,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Marker Planning',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to marker planning
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMarkerItem(
              'Marker-001',
              'Shirt - Size S,M,L,XL',
              '95%',
              'Approved',
              Colors.green,
            ),
            _buildMarkerItem(
              'Marker-002',
              'Trouser - Size 30-38',
              '92%',
              'In Review',
              Colors.orange,
            ),
            _buildMarkerItem(
              'Marker-003',
              'Dress - Size XS-L',
              '88%',
              'Draft',
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarkerItem(String markerId, String description, String efficiency, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  markerId,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
                Text(
                  'Efficiency: $efficiency',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFabricUtilization() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.pie_chart,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Fabric Utilization',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Excellent',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildUtilizationCard('Today', '94%', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('This Week', '91%', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('This Month', '89%', Colors.purple),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('Target', '90%', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUtilizationCard(String period, String percentage, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            percentage,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            period,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // CRUD Dialog Methods

  /// Show Create Marker Dialog
  void _showCreateMarkerDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final markerNumberController = TextEditingController();
    final productIdController = TextEditingController();
    final productNameController = TextEditingController();
    final fabricTypeController = TextEditingController();
    final fabricWidthController = TextEditingController();
    final markerLengthController = TextEditingController();
    final piecesPerMarkerController = TextEditingController();
    final efficiencyController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Marker'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: markerNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Marker Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: productIdController,
                    decoration: const InputDecoration(
                      labelText: 'Product ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: productNameController,
                    decoration: const InputDecoration(
                      labelText: 'Product Name',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: fabricTypeController,
                    decoration: const InputDecoration(
                      labelText: 'Fabric Type',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: fabricWidthController,
                          decoration: const InputDecoration(
                            labelText: 'Fabric Width (cm)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: markerLengthController,
                          decoration: const InputDecoration(
                            labelText: 'Marker Length (cm)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: piecesPerMarkerController,
                          decoration: const InputDecoration(
                            labelText: 'Pieces per Marker',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: efficiencyController,
                          decoration: const InputDecoration(
                            labelText: 'Efficiency (%)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes (Optional)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _createMarker(context, formKey, {
              'markerNumber': markerNumberController.text,
              'productId': productIdController.text,
              'productName': productNameController.text,
              'fabricType': fabricTypeController.text,
              'fabricWidth': fabricWidthController.text,
              'markerLength': markerLengthController.text,
              'piecesPerMarker': piecesPerMarkerController.text,
              'efficiency': efficiencyController.text,
              'notes': notesController.text,
            }),
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  /// Show Start Cutting Dialog
  void _showStartCuttingDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final taskNumberController = TextEditingController();
    final markerIdController = TextEditingController();
    final plannedQuantityController = TextEditingController();
    final assignedToController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start Cutting Task'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: taskNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Task Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: markerIdController,
                    decoration: const InputDecoration(
                      labelText: 'Marker ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: plannedQuantityController,
                    decoration: const InputDecoration(
                      labelText: 'Planned Quantity',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: assignedToController,
                    decoration: const InputDecoration(
                      labelText: 'Assigned To',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes (Optional)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _startCuttingTask(context, formKey, {
              'taskNumber': taskNumberController.text,
              'markerId': markerIdController.text,
              'plannedQuantity': plannedQuantityController.text,
              'assignedTo': assignedToController.text,
              'notes': notesController.text,
            }),
            child: const Text('Start Task'),
          ),
        ],
      ),
    );
  }

  /// Show Fabric Calculator Dialog
  void _showFabricCalculatorDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final calculationNumberController = TextEditingController();
    final productIdController = TextEditingController();
    final productNameController = TextEditingController();
    final fabricTypeController = TextEditingController();
    final fabricWidthController = TextEditingController();
    final wastagePercentageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fabric Calculator'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: calculationNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Calculation Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: productIdController,
                    decoration: const InputDecoration(
                      labelText: 'Product ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: productNameController,
                    decoration: const InputDecoration(
                      labelText: 'Product Name',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: fabricTypeController,
                    decoration: const InputDecoration(
                      labelText: 'Fabric Type',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: fabricWidthController,
                          decoration: const InputDecoration(
                            labelText: 'Fabric Width (cm)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: wastagePercentageController,
                          decoration: const InputDecoration(
                            labelText: 'Wastage %',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _calculateFabric(context, formKey, {
              'calculationNumber': calculationNumberController.text,
              'productId': productIdController.text,
              'productName': productNameController.text,
              'fabricType': fabricTypeController.text,
              'fabricWidth': fabricWidthController.text,
              'wastagePercentage': wastagePercentageController.text,
            }),
            child: const Text('Calculate'),
          ),
        ],
      ),
    );
  }

  /// Show Quality Check Dialog
  void _showQualityCheckDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final taskIdController = TextEditingController();
    final checkTypeController = TextEditingController();
    final scoreController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quality Check'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: taskIdController,
                  decoration: const InputDecoration(
                    labelText: 'Task ID',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: checkTypeController,
                  decoration: const InputDecoration(
                    labelText: 'Check Type',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: scoreController,
                  decoration: const InputDecoration(
                    labelText: 'Quality Score (0-100)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _addQualityCheck(context, formKey, {
              'taskId': taskIdController.text,
              'checkType': checkTypeController.text,
              'score': scoreController.text,
              'notes': notesController.text,
            }),
            child: const Text('Add Check'),
          ),
        ],
      ),
    );
  }

  /// Show Task Update Dialog
  void _showTaskUpdateDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final taskIdController = TextEditingController();
    final actualQuantityController = TextEditingController();
    final statusController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Task'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: taskIdController,
                  decoration: const InputDecoration(
                    labelText: 'Task ID',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: actualQuantityController,
                  decoration: const InputDecoration(
                    labelText: 'Actual Quantity',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: statusController.text.isEmpty ? null : statusController.text,
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'pending', child: Text('Pending')),
                    DropdownMenuItem(value: 'inProgress', child: Text('In Progress')),
                    DropdownMenuItem(value: 'completed', child: Text('Completed')),
                    DropdownMenuItem(value: 'onHold', child: Text('On Hold')),
                  ],
                  onChanged: (value) => statusController.text = value ?? '',
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _updateTask(context, formKey, {
              'taskId': taskIdController.text,
              'actualQuantity': actualQuantityController.text,
              'status': statusController.text,
              'notes': notesController.text,
            }),
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  /// Show Production Log Dialog
  void _showProductionLogDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final logNumberController = TextEditingController();
    final taskIdController = TextEditingController();
    final taskTypeController = TextEditingController();
    final operatorIdController = TextEditingController();
    final operatorNameController = TextEditingController();
    final quantityProducedController = TextEditingController();
    final qualityScoreController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Production Log'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: logNumberController,
                    decoration: const InputDecoration(
                      labelText: 'Log Number',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: taskIdController,
                    decoration: const InputDecoration(
                      labelText: 'Task ID',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: taskTypeController,
                    decoration: const InputDecoration(
                      labelText: 'Task Type',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value?.isEmpty == true ? 'Required' : null,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: operatorIdController,
                          decoration: const InputDecoration(
                            labelText: 'Operator ID',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: operatorNameController,
                          decoration: const InputDecoration(
                            labelText: 'Operator Name',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: quantityProducedController,
                          decoration: const InputDecoration(
                            labelText: 'Quantity Produced',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) => value?.isEmpty == true ? 'Required' : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: qualityScoreController,
                          decoration: const InputDecoration(
                            labelText: 'Quality Score',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _createProductionLog(context, formKey, {
              'logNumber': logNumberController.text,
              'taskId': taskIdController.text,
              'taskType': taskTypeController.text,
              'operatorId': operatorIdController.text,
              'operatorName': operatorNameController.text,
              'quantityProduced': quantityProducedController.text,
              'qualityScore': qualityScoreController.text,
              'notes': notesController.text,
            }),
            child: const Text('Create Log'),
          ),
        ],
      ),
    );
  }

  // CRUD Functions

  /// Create Marker in Firebase
  Future<void> _createMarker(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      await firestore.collection('markers').add({
        'markerNumber': data['markerNumber'],
        'productId': data['productId'],
        'productName': data['productName'],
        'fabricType': data['fabricType'],
        'fabricWidth': double.tryParse(data['fabricWidth'] ?? '0') ?? 0.0,
        'markerLength': double.tryParse(data['markerLength'] ?? '0') ?? 0.0,
        'piecesPerMarker': int.tryParse(data['piecesPerMarker'] ?? '0') ?? 0,
        'pieces': [], // Empty for now, can be added later
        'status': 'draft',
        'createdBy': 'current_user_id', // Replace with actual user ID
        'createdByName': 'Current User', // Replace with actual user name
        'efficiency': double.tryParse(data['efficiency'] ?? '0') ?? 0.0,
        'notes': data['notes'],
        'specifications': {},
        'createdAt': now,
        'updatedAt': now,
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Marker created successfully!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating marker: $e')),
      );
    }
  }

  /// Start Cutting Task in Firebase
  Future<void> _startCuttingTask(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      await firestore.collection('cutting_tasks').add({
        'taskNumber': data['taskNumber'],
        'markerId': data['markerId'],
        'markerNumber': 'MARKER-${data['markerId']}', // Simplified
        'productId': 'PROD-001', // Simplified
        'productName': 'Sample Product', // Simplified
        'fabricType': 'Cotton', // Simplified
        'plannedQuantity': int.tryParse(data['plannedQuantity'] ?? '0') ?? 0,
        'actualQuantity': 0,
        'status': 'pending',
        'assignedTo': data['assignedTo'],
        'assignedToName': data['assignedTo'], // Simplified
        'assignedBy': 'current_user_id', // Replace with actual user ID
        'assignedByName': 'Current User', // Replace with actual user name
        'plannedStartDate': now,
        'plannedEndDate': Timestamp.fromDate(DateTime.now().add(const Duration(days: 1))),
        'cutPieces': [],
        'qualityChecks': [],
        'notes': data['notes'],
        'metadata': {},
        'createdAt': now,
        'updatedAt': now,
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cutting task started successfully!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error starting cutting task: $e')),
      );
    }
  }

  /// Calculate Fabric in Firebase
  Future<void> _calculateFabric(BuildContext context, GlobalKey<FormState> formKey, Map<String, String> data) async {
    if (!formKey.currentState!.validate()) return;

    try {
      final firestore = FirebaseFirestore.instance;
      final now = Timestamp.now();

      final fabricWidth = double.tryParse(data['fabricWidth'] ?? '0') ?? 0.0;
      final wastagePercentage = double.tryParse(data['wastagePercentage'] ?? '0') ?? 0.0;

      // Simplified calculation - in real app, this would be more complex
      final totalFabricRequired = fabricWidth * 2.0; // Simplified
      final totalFabricWithWastage = totalFabricRequired * (1 + wastagePercentage / 100);

      await firestore.collection('fabric_calculations').add({
        'calculationNumber': data['calculationNumber'],
        'productId': data['productId'],
        'productName': data['productName'],
        'fabricType': data['fabricType'],
        'fabricWidth': fabricWidth,
        'sizeQuantities': {'S': 10, 'M': 15, 'L': 12, 'XL': 8}, // Simplified
        'totalFabricRequired': totalFabricRequired,
        'wastagePercentage': wastagePercentage,
        'totalFabricWithWastage': totalFabricWithWastage,
        'calculatedBy': 'current_user_id', // Replace with actual user ID
        'calculatedByName': 'Current User', // Replace with actual user name
        'status': 'draft',
        'createdAt': now,
        'updatedAt': now,
      });

      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Fabric calculation completed! Total required: ${totalFabricWithWastage.toStringAsFixed(2)} meters')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error calculating fabric: $e')),
      );
    }
  }
