import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/widgets/dashboard_card.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';

/// Cutting Master Dashboard Page - Fabric cutting and marker planning
class CuttingMasterDashboardPage extends StatelessWidget {
  const CuttingMasterDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cutting Master Dashboard'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // Handle analytics
            },
          ),
        ],
      ),
      body: BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
        builder: (context, state) {
          if (state is FirebaseAuthAuthenticated) {
            return _buildCuttingMasterDashboard(context, state.user);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildCuttingMasterDashboard(BuildContext context, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(user),
          const SizedBox(height: 24),
          _buildWorkOverview(),
          const SizedBox(height: 24),
          _buildCuttingActions(context),
          const SizedBox(height: 24),
          _buildMyTasks(),
          const SizedBox(height: 24),
          _buildMarkerPlanning(),
          const SizedBox(height: 24),
          _buildFabricUtilization(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.deepPurple,
            Colors.deepPurple.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user.role?.displayName ?? 'Cutting Master'}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Fabric cutting and marker planning',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.design_services,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildWorkOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Today\'s Work Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.8,
          children: const [
            DashboardMetricCard(
              title: 'My Tasks',
              value: '6',
              icon: Icons.assignment,
              color: Colors.blue,
              trend: '+2',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Completed',
              value: '4',
              icon: Icons.check_circle,
              color: Colors.green,
              trend: '+1',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Fabric Efficiency',
              value: '94%',
              icon: Icons.trending_up,
              color: Colors.purple,
              trend: '+2%',
              isPositiveTrend: true,
            ),
            DashboardMetricCard(
              title: 'Markers Created',
              value: '8',
              icon: Icons.straighten,
              color: Colors.orange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCuttingActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cutting Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 2.8,
          children: [
            DashboardCard(
              title: 'Create Marker',
              subtitle: 'Design cutting markers',
              icon: Icons.straighten,
              color: Colors.blue,
              onTap: () {
                // Navigate to marker creation
              },
            ),
            DashboardCard(
              title: 'Start Cutting',
              subtitle: 'Begin cutting process',
              icon: Icons.content_cut,
              color: Colors.green,
              onTap: () {
                // Navigate to cutting process
              },
            ),
            DashboardCard(
              title: 'Fabric Calculator',
              subtitle: 'Calculate fabric requirements',
              icon: Icons.calculate,
              color: Colors.purple,
              onTap: () {
                // Navigate to fabric calculator
              },
            ),
            DashboardCard(
              title: 'Quality Check',
              subtitle: 'Inspect cut pieces',
              icon: Icons.verified,
              color: Colors.orange,
              onTap: () {
                // Navigate to quality check
              },
            ),
            DashboardCard(
              title: 'Task Updates',
              subtitle: 'Update task progress',
              icon: Icons.update,
              color: Colors.teal,
              onTap: () {
                // Navigate to task updates
              },
            ),
            DashboardCard(
              title: 'Production Log',
              subtitle: 'Log production data',
              icon: Icons.history,
              color: Colors.indigo,
              onTap: () {
                // Navigate to production log
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMyTasks() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.assignment,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'My Cutting Tasks',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to all tasks
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTaskItem(
              'CUT-001',
              'Shirt cutting - Order #1234',
              'High',
              'In Progress',
              Colors.blue,
              0.65,
            ),
            _buildTaskItem(
              'CUT-002',
              'Trouser cutting - Order #1235',
              'Medium',
              'Pending',
              Colors.orange,
              0.0,
            ),
            _buildTaskItem(
              'CUT-003',
              'Dress cutting - Order #1236',
              'High',
              'Completed',
              Colors.green,
              1.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskItem(String taskId, String description, String priority, String status, Color color, double progress) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      taskId,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getPriorityColor(priority).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  priority,
                  style: TextStyle(
                    color: _getPriorityColor(priority),
                    fontWeight: FontWeight.w500,
                    fontSize: 10,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget _buildMarkerPlanning() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.straighten,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Marker Planning',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to marker planning
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMarkerItem(
              'Marker-001',
              'Shirt - Size S,M,L,XL',
              '95%',
              'Approved',
              Colors.green,
            ),
            _buildMarkerItem(
              'Marker-002',
              'Trouser - Size 30-38',
              '92%',
              'In Review',
              Colors.orange,
            ),
            _buildMarkerItem(
              'Marker-003',
              'Dress - Size XS-L',
              '88%',
              'Draft',
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarkerItem(String markerId, String description, String efficiency, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  markerId,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                ),
                Text(
                  'Efficiency: $efficiency',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFabricUtilization() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.pie_chart,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Fabric Utilization',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Excellent',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildUtilizationCard('Today', '94%', Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('This Week', '91%', Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('This Month', '89%', Colors.purple),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildUtilizationCard('Target', '90%', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUtilizationCard(String period, String percentage, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            percentage,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            period,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
