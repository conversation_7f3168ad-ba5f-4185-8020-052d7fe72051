import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:fl_chart/fl_chart.dart';
import '../widgets/quality_statistics_card.dart';
import '../../../../core/constants/app_constants.dart';
import '../../domain/entities/quality_entities.dart';
import '../../domain/entities/quality_inspection_filter.dart';
import '../../domain/repositories/quality_repository.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/quality_entities.dart';
import '../../domain/repositories/quality_repository.dart';
import '../bloc/quality_bloc.dart'; 
import '../widgets/quality_app_bar.dart';
import '../widgets/quality_inspection_list_view_new.dart';
import '../widgets/quality_search_bar.dart';
import '../widgets/quality_statistics_card_new.dart' hide QualityStatisticsCard;

/// Quality control page
class QualityPage extends StatefulWidget {
  const QualityPage({super.key});

  @override
  State<QualityPage> createState() => _QualityPageState();
}

class _QualityPageState extends State<QualityPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  QualityInspectionFilter? _currentFilter;
  String? _searchQuery;
  QualityViewMode _viewMode = QualityViewMode.list;
  bool _showSearchBar = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<QualityBloc>()
        ..add(const LoadQualityInspectionsRequested()),
      child: Scaffold(
        appBar: QualitySearchAppBar(
          title: 'Quality Control',
          hintText: 'Search quality inspections...',
          onSearchChanged: _handleSearch,
          onSearchClear: _clearSearch,
          onFilterPressed: _showFilterDialog,
          isSearching: _showSearchBar,
          onViewModeChanged: _handleViewModeChanged,
          currentViewMode: _viewMode,
          onAddPressed: _navigateToCreateInspection,
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAllInspectionsTab(),
                  _buildPendingInspectionsTab(),
                  _buildInProgressInspectionsTab(),
                  _buildCompletedInspectionsTab(),
                  _buildFailedInspectionsTab(),
                  _buildDefectsTab(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _navigateToCreateInspection,
          tooltip: 'Create Quality Inspection',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(text: 'All Inspections'),
          Tab(text: 'Pending'),
          Tab(text: 'In Progress'),
          Tab(text: 'Completed'),
          Tab(text: 'Failed'),
          Tab(text: 'Defects'),
        ],
        onTap: _handleTabChanged,
      ),
    );
  }

  Widget _buildAllInspectionsTab() {
    return BlocBuilder<QualityBloc, QualityState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            _buildStatisticsSection(),
            Expanded(
              child: _buildQualityInspectionsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPendingInspectionsTab() {
    return BlocBuilder<QualityBloc, QualityState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildPendingInspectionsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInProgressInspectionsTab() {
    return BlocBuilder<QualityBloc, QualityState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildQualityInspectionsList(state, status: InspectionStatus.inProgress),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCompletedInspectionsTab() {
    return BlocBuilder<QualityBloc, QualityState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildQualityInspectionsList(state, status: InspectionStatus.completed),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFailedInspectionsTab() {
    return BlocBuilder<QualityBloc, QualityState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildFailedInspectionsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDefectsTab() {
    return BlocBuilder<QualityBloc, QualityState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildQualityDefectsList(state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchBar() {
    if (!_showSearchBar) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: 8.0,
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: TextEditingController(text: _searchQuery ?? ''),
              decoration: InputDecoration(
                hintText: 'Search quality inspections...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery?.isNotEmpty == true
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: _clearSearch,
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                isDense: true,
              ),
              onChanged: _handleSearch,
              onSubmitted: _handleSearch,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              setState(() {
                _showSearchBar = false;
                _searchQuery = null;
                _clearSearch();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return BlocBuilder<QualityBloc, QualityState>(
      builder: (context, state) {
        if (state is QualityInspectionsLoaded && state.inspections.isNotEmpty) {
          final statistics = _calculateStatistics(state.inspections);
          return Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            child: QualityStatisticsCard(
              statistics: statistics,
              onTap: () => _handleStatisticTap('overview'),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  QualityStatistics _calculateStatistics(List<QualityInspection> inspections) {
    final completedInspections = inspections.where((i) => i.isCompleted).toList();
    final totalInspections = completedInspections.length;
    
    // Calculate basic statistics
    final passedInspections = completedInspections.where((i) => i.isPassed).length;
    final failedInspections = totalInspections - passedInspections;
    final passRate = totalInspections > 0 ? (passedInspections / totalInspections) * 100 : 0.0;
    
    // Calculate defects
    int totalDefects = 0;
    int criticalDefects = 0;
    int majorDefects = 0;
    int minorDefects = 0;
    
    // Track defects by category
    final defectsByCategory = <DefectCategory, int>{};
    // Track inspections by stage
    final inspectionsByStage = <String, int>{};
    // Track quality score by product
    final qualityScoreByProduct = <String, double>{};
    
    for (final inspection in completedInspections) {
      // Count total defects by severity
      totalDefects += inspection.defects.length;
      criticalDefects += inspection.defects.where((d) => d.severity == DefectSeverity.critical).length;
      majorDefects += inspection.defects.where((d) => d.severity == DefectSeverity.major).length;
      minorDefects += inspection.defects.where((d) => d.severity == DefectSeverity.minor).length;
      
      // Count defects by category
      for (final defect in inspection.defects) {
        defectsByCategory.update(
          defect.category, 
          (count) => count + 1, 
          ifAbsent: () => 1
        );
      }
      
      // Count inspections by stage
      inspectionsByStage.update(
        inspection.stage.name, // Convert enum to string using .name
        (count) => count + 1, 
        ifAbsent: () => 1
      );
      
      // Calculate average quality score by product
      final productName = inspection.product?.name ?? 'Unknown';
      final currentScore = qualityScoreByProduct[productName] ?? 0.0;
      final inspectionScore = inspection.result.qualityScore ?? 0.0;
      qualityScoreByProduct[productName] = (currentScore + inspectionScore) / 2;
    }
    
    final defectRate = totalInspections > 0 ? (totalDefects / totalInspections) * 100 : 0.0;
    
    // Calculate first pass yield (FPY)
    final firstPassYield = totalInspections > 0 
        ? (completedInspections.where((i) => i.isPassed && i.defects.isEmpty).length / totalInspections) * 100 
        : 0.0;
    
    // Calculate average inspection time in minutes
    final totalInspectionTime = completedInspections.fold<Duration>(
      Duration.zero,
      (sum, i) => i.completedDate != null 
          ? sum + i.completedDate!.difference(i.inspectionDate) 
          : sum,
    );
    final averageInspectionTime = totalInspections > 0 
        ? totalInspectionTime.inMinutes / totalInspections 
        : 0.0;
    
    // Reuse the existing inspectionsByStage map that was already populated
    // Clear any existing entries to ensure we're only counting completed inspections
    inspectionsByStage.clear();
    for (final inspection in completedInspections) {
      final stage = inspection.stage.toString();
      inspectionsByStage[stage] = (inspectionsByStage[stage] ?? 0) + 1;
    }
    
    // The defectsByCategory and qualityScoreByProduct are already calculated above in the first loop
    // No need to recalculate them here
    
    // Clear and recalculate quality scores using orderNumber instead of product name
    qualityScoreByProduct.clear();
    double totalScores = 0.0;
    int validScores = 0;
    
    for (final inspection in completedInspections) {
      final productId = inspection.orderNumber;
      final currentScore = qualityScoreByProduct[productId] ?? 0.0;
      final inspectionScore = inspection.result.qualityScore ?? 0.0;
      qualityScoreByProduct[productId] = (currentScore + inspectionScore) / 2;
      
      // Calculate total scores for average
      if (inspection.result.qualityScore != null) {
        totalScores += inspection.result.qualityScore!;
        validScores++;
      }
    }
    
    // Calculate average score, default to 0 if no valid scores
    final double averageScore = validScores > 0 ? totalScores / validScores : 0.0;
    
    return QualityStatistics(
      totalInspections: totalInspections,
      passedInspections: passedInspections,
      failedInspections: failedInspections,
      averageScore: averageScore,
      firstPassYield: firstPassYield,
      criticalDefects: criticalDefects,
      majorDefects: majorDefects,
      minorDefects: minorDefects,
      inspectionsByStage: inspectionsByStage,
    );
  
  }

  Widget _buildQualityInspectionsList(QualityState state, {InspectionStatus? status}) {
    if (state is QualityLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is QualityError) {
      return _buildErrorWidget(state.message);
    }

    if (state is QualityInspectionsLoaded) {
      var inspections = state.inspections;
      
      // Filter by status if specified
      if (status != null) {
        inspections = inspections.where((inspection) => inspection.status == status).toList();
      }

      if (inspections.isEmpty) {
        return _buildEmptyState(status);
      }

      return RefreshIndicator(
        onRefresh: () async => _refreshQualityInspections(),
        child: QualityInspectionListView(
          inspections: inspections,
          viewMode: state.viewMode,
          selectedInspectionIds: state.selectedInspectionIds,
          onInspectionTap: _navigateToInspectionDetails,
          onInspectionLongPress: _handleInspectionLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStatusChanged: _handleStatusChanged,
          onLoadMore: _loadMoreInspections,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: state.isRefreshing,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildPendingInspectionsList(QualityState state) {
    if (state is QualityLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is QualityError) {
      return _buildErrorWidget(state.message);
    }

    if (state is PendingInspectionsLoaded) {
      if (state.inspections.isEmpty) {
        return _buildEmptyState(null, isPending: true);
      }

      return RefreshIndicator(
        onRefresh: () async => _loadPendingInspections(),
        child: QualityInspectionListView(
          inspections: state.inspections,
          viewMode: _viewMode,
          selectedInspectionIds: const [],
          onInspectionTap: _navigateToInspectionDetails,
          onInspectionLongPress: _handleInspectionLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStatusChanged: _handleStatusChanged,
          onLoadMore: _loadMoreInspections,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildFailedInspectionsList(QualityState state) {
    if (state is QualityLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is QualityError) {
      return _buildErrorWidget(state.message);
    }

    if (state is FailedInspectionsLoaded) {
      if (state.inspections.isEmpty) {
        return _buildEmptyState(null, isFailed: true);
      }

      return RefreshIndicator(
        onRefresh: () async => _loadFailedInspections(),
        child: QualityInspectionListView(
          inspections: state.inspections,
          viewMode: _viewMode,
          selectedInspectionIds: const [],
          onInspectionTap: _navigateToInspectionDetails,
          onInspectionLongPress: _handleInspectionLongPress,
          onSelectionChanged: _handleSelectionChanged,
          onStatusChanged: _handleStatusChanged,
          onLoadMore: _loadMoreInspections,
          hasMore: state.pagination?.hasNextPage ?? false,
          isLoading: false,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildQualityDefectsList(QualityState state) {
    if (state is QualityLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is QualityError) {
      return _buildErrorWidget(state.message);
    }

    if (state is QualityDefectsLoaded) {
      if (state.defects.isEmpty) {
        return _buildEmptyState(null, isDefects: true);
      }

      return RefreshIndicator(
        onRefresh: () async => _loadQualityDefects(),
        child: ListView.builder(
          itemCount: state.defects.length,
          itemBuilder: (context, index) {
            final defect = state.defects[index];
            return Card(
              margin: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: 4,
              ),
              child: ListTile(
                leading: Icon(
                  _getDefectIcon(defect.severity),
                  color: _getDefectColor(defect.severity),
                ),
                title: Text(defect.defectName),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(defect.description),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Chip(
                          label: Text(defect.severity.displayName),
                          backgroundColor: _getDefectColor(defect.severity).withOpacity(0.1),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          defect.location,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      defect.status.name.toUpperCase(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: defect.isResolved ? Colors.green : Colors.orange,
                      ),
                    ),
                    Text(
                      defect.detectedAt.toString().split(' ')[0],
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                onTap: () => _navigateToDefectDetails(defect.id),
              ),
            );
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading quality data',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _refreshQualityInspections,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(InspectionStatus? status, {
    bool isPending = false,
    bool isFailed = false,
    bool isDefects = false,
  }) {
    String title;
    String subtitle;
    IconData icon;

    if (isPending) {
      title = 'No Pending Inspections';
      subtitle = 'All inspections are up to date.';
      icon = Icons.check_circle_outline;
    } else if (isFailed) {
      title = 'No Failed Inspections';
      subtitle = 'Great! All inspections are passing quality standards.';
      icon = Icons.verified_outlined;
    } else if (isDefects) {
      title = 'No Quality Defects';
      subtitle = 'Excellent! No defects have been recorded.';
      icon = Icons.high_quality_outlined;
    } else if (status != null) {
      title = 'No ${status.name} Inspections';
      subtitle = 'No quality inspections found with ${status.name} status.';
      icon = Icons.search_off;
    } else {
      title = 'No Quality Inspections Found';
      subtitle = 'Create your first quality inspection to get started.';
      icon = Icons.fact_check_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (!isPending && !isFailed && !isDefects) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _navigateToCreateInspection,
              child: const Text('Create Quality Inspection'),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getDefectIcon(DefectSeverity severity) {
    switch (severity) {
      case DefectSeverity.critical:
        return Icons.error;
      case DefectSeverity.major:
        return Icons.warning;
      case DefectSeverity.minor:
        return Icons.info;
    }
  }

  Color _getDefectColor(DefectSeverity severity) {
    switch (severity) {
      case DefectSeverity.critical:
        return Colors.red;
      case DefectSeverity.major:
        return Colors.orange;
      case DefectSeverity.minor:
        return Colors.blue;
    }
  }

  void _handleTabChanged(int index) {
    switch (index) {
      case 0:
        context.read<QualityBloc>().add(const LoadQualityInspectionsRequested());
        break;
      case 1:
        _loadPendingInspections();
        break;
      case 2:
        context.read<QualityBloc>().add(const FilterQualityInspectionsRequested(
          QualityInspectionFilter(status: 'inProgress'),
        ));
        break;
      case 3:
        context.read<QualityBloc>().add(const FilterQualityInspectionsRequested(
          QualityInspectionFilter(status: 'completed'),
        ));
        break;
      case 4:
        _loadFailedInspections();
        break;
      case 5:
        _loadQualityDefects();
        break;
    }
  }

  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query.isEmpty ? null : query;
    });

    if (query.isEmpty) {
      context.read<QualityBloc>().add(LoadQualityInspectionsRequested(filter: _currentFilter));
    } else {
      context.read<QualityBloc>().add(SearchQualityInspectionsRequested(
        query,
        filter: _currentFilter,
      ));
    }
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = null;
    });
    context.read<QualityBloc>().add(LoadQualityInspectionsRequested(filter: _currentFilter));
  }

  void _showFilterDialog() async {
    // TODO: Implement filter dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Filter dialog coming soon')),
    );
  }

  void _handleViewModeChanged(QualityViewMode viewMode) {
    setState(() {
      _viewMode = viewMode;
    });
    context.read<QualityBloc>().add(ChangeQualityViewModeRequested(viewMode));
  }

  void _handleStatisticTap(String statistic) {
    // Handle statistic tap for filtering or navigation
    switch (statistic) {
      case 'pending':
        _tabController.animateTo(1);
        break;
      case 'in_progress':
        _tabController.animateTo(2);
        break;
      case 'failed':
        _tabController.animateTo(4);
        break;
      case 'defects':
        _tabController.animateTo(5);
        break;
    }
  }

  void _navigateToInspectionDetails(QualityInspection inspection) {
    // TODO: Navigate to quality inspection details page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Navigate to inspection ${inspection.id} details')),
    );
  }

  void _navigateToDefectDetails(String defectId) {
    // TODO: Navigate to quality defect details page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Navigate to defect $defectId details')),
    );
  }

  void _navigateToCreateInspection() {
    // TODO: Navigate to create quality inspection page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create Quality Inspection feature coming soon')),
    );
  }

  void _handleInspectionLongPress(String inspectionId) {
    context.read<QualityBloc>().add(SelectQualityInspectionRequested(inspectionId));
  }

  void _handleSelectionChanged(List<String> selectedIds) {
    // Handle selection change for bulk operations
  }

  void _handleStatusChanged(QualityInspection inspection) {
    // Handle status change
    if (inspection.status == InspectionStatus.inProgress) {
      context.read<QualityBloc>().add(StartQualityInspectionRequested(inspection.id));
    }
  }

  void _refreshQualityInspections() {
    context.read<QualityBloc>().add(const RefreshQualityInspectionsRequested());
  }

  void _loadMoreInspections() {
    context.read<QualityBloc>().add(const LoadMoreQualityInspectionsRequested());
  }

  void _loadPendingInspections() {
    context.read<QualityBloc>().add(const LoadPendingInspectionsRequested());
  }

  void _loadFailedInspections() {
    context.read<QualityBloc>().add(const LoadFailedInspectionsRequested());
  }

  void _loadQualityDefects() {
    context.read<QualityBloc>().add(const LoadQualityDefectsRequested());
  }
}
